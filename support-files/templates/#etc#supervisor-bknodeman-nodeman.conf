[supervisord]
loglevel=info
logfile=__BK_HOME__/logs/bknodeman/nodeman-supervisord.log
pidfile=/var/run/bknodeman/nodeman-supervisord.pid
umask=022
nodaemon=false

[program:nodeman_api]
command=/bin/bash -c "source bin/environ.sh && exec gunicorn --timeout 300 -w 8 -b __LAN_IP__:__BK_NODEMAN_API_PORT__ --max-requests=10000 --max-requests-jitter 1000 -k gthread --threads 10 wsgi:application"
numprocs=1
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-api.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_sync_host_event]
command=/bin/bash -c "source bin/environ.sh && python manage.py sync_host_event"
numprocs=1
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-resource-watch.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_sync_host_relation_event]
command=/bin/bash -c "source bin/environ.sh && python manage.py sync_host_relation_event"
numprocs=1
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-resource-watch.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_sync_process_event]
command=/bin/bash -c "source bin/environ.sh && python manage.py sync_process_event"
numprocs=1
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-resource-watch.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_apply_resource_watched_events]
command=/bin/bash -c "source bin/environ.sh && python manage.py apply_resource_watched_events"
numprocs=1
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-resource-watch.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_celery_beat]
command=/bin/bash -c "sleep 10 && source bin/environ.sh && exec celery -A apps.backend beat -l info  -S redbeat.RedBeatScheduler --pidfile /var/run/bknodeman/celerybeat.pid"
numprocs=1
process_name = %(program_name)s_%(process_num)02d
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-celery-beat.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_celery_default]
command=/bin/bash -c "sleep 10 && source bin/environ.sh && exec celery -A apps.backend worker -Q default -n default_%(process_num)02d@%%h -c 3 --maxtasksperchild=50 -O fair --time-limit=1800"
numprocs=1
process_name = %(program_name)s_%(process_num)02d
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-celery.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_celery_backend]
command=/bin/bash -c "sleep 10 && source bin/environ.sh && exec bin/hooks/start_cmds/celery/start-bworker"
numprocs=1
process_name = %(program_name)s_%(process_num)02d
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-celery.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_celery_backend_additional]
command=/bin/bash -c "sleep 10 && source bin/environ.sh && exec celery -A apps.backend worker -Q backend_additional_task -n baworker_%(process_num)02d@%%h -c 10 -O fair --time-limit=1800 --maxtasksperchild=50"
numprocs=1
process_name = %(program_name)s_%(process_num)02d
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-celery.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_pipeline_worker]
command=/bin/bash -c "sleep 10 && source bin/environ.sh && exec bin/hooks/start_cmds/celery/start-pworker"
numprocs=3
process_name=%(program_name)s_%(process_num)02d
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-pipeline.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_pipeline_schedule]
command=/bin/bash -c "sleep 10 && source bin/environ.sh && exec bin/hooks/start_cmds/celery/start-psworker"
numprocs=3
process_name=%(program_name)s_%(process_num)02d
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-pipeline.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[program:nodeman_pipeline_additional]
command=/bin/bash -c "sleep 10 && source bin/environ.sh && exec bin/hooks/start_cmds/celery/start-paworker"
numprocs=1
process_name=%(program_name)s_%(process_num)02d
autostart=true
autorestart=true
startretries=3
stopsignal=TERM
stopasgroup=true
stdout_logfile=__BK_HOME__/logs/bknodeman/nodeman-pipeline.log
redirect_stderr=true
directory=__BK_HOME__/bknodeman/nodeman

[unix_http_server]
file =/var/run/bknodeman/nodeman-supervisord.sock
chmod = 0777

[supervisorctl]
serverurl = unix:///var/run/bknodeman/nodeman-supervisord.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
