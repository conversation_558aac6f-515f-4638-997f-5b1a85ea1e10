{"system_id": "bk_nodeman", "operations": [{"operation": "update_instance_selection", "data": {"id": "cloud_instance_selection", "name": "管控区域", "name_en": "BK-Net", "resource_type_chain": [{"system_id": "bk_nodeman", "id": "cloud"}]}}, {"operation": "update_resource_type", "data": {"id": "cloud", "name": "管控区域", "name_en": "BK-Net", "description": "", "description_en": "", "provider_config": {"path": "/api/iam/v1/cloud"}, "version": 1}}, {"operation": "update_action", "data": {"id": "cloud_view", "name": "管控区域查看", "name_en": "BK-Net View"}}, {"operation": "update_action", "data": {"id": "cloud_edit", "name": "管控区域编辑", "name_en": "BK-Net Edit"}}, {"operation": "update_action", "data": {"id": "cloud_delete", "name": "管控区域删除", "name_en": "BK-Net Delete"}}, {"operation": "update_action", "data": {"id": "cloud_create", "name": "管控区域创建", "name_en": "BK-Net Create"}}, {"operation": "upsert_action_groups", "data": [{"name": "常规功能", "name_en": "Normal", "sub_groups": [{"name": "Agent管理", "name_en": "Agent", "actions": [{"id": "agent_view"}, {"id": "agent_operate"}]}, {"name": "管控区域管理", "name_en": "BK-Net", "actions": [{"id": "cloud_create"}, {"id": "cloud_edit"}, {"id": "cloud_delete"}, {"id": "cloud_view"}, {"id": "proxy_operate"}]}, {"name": "插件管理", "name_en": "Plugin", "actions": [{"id": "plugin_pkg_import"}, {"id": "plugin_pkg_operate"}, {"id": "plugin_view"}, {"id": "plugin_operate"}, {"id": "strategy_create"}, {"id": "strategy_view"}, {"id": "strategy_operate"}]}, {"name": "任务历史", "name_en": "Task History", "actions": [{"id": "task_history_view"}]}]}, {"name": "全局配置", "name_en": "Globle Configuration", "sub_groups": [{"name": "接入点管理", "name_en": "Access Point", "actions": [{"id": "ap_create"}, {"id": "ap_delete"}, {"id": "ap_edit"}, {"id": "ap_view"}]}, {"name": "任务配置", "name_en": "Task Configuration", "actions": [{"id": "globe_task_config"}]}]}]}]}