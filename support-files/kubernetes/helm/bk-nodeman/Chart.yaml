apiVersion: v2
name: bk-nodeman
description: NodeMan is an application that helps operation and maintenance personnel
  to realize Agent installation, plug-in management and other back-end service management.

# A chart can be either an 'application' or a 'library' chart.
#
# Application charts are a collection of templates that can be packaged into versioned archives
# to be deployed.
#
# Library charts provide useful utilities or functions for the chart developer. They're included as
# a dependency of application charts to inject those utilities and functions into the rendering
# pipeline. Library charts do not define any templates and therefore cannot be deployed.
type: application

# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 2.2.32

# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: "2.2.32"

dependencies:
- name: common
  version: 1.x.x
  repository: https://charts.bitnami.com/bitnami
- name: mysql
  version: 9.x.x
  repository: https://charts.bitnami.com/bitnami
  condition: mysql.enabled
- name: redis
  version: 16.x.x
  repository: https://charts.bitnami.com/bitnami
  condition: redis.enabled
- name: rabbitmq
  version: 11.x.x
  repository: https://charts.bitnami.com/bitnami
  condition: rabbitmq.enabled
- name: nginx-ingress-controller
  version: 9.x.x
  repository: https://charts.bitnami.com/bitnami
  condition: nginx-ingress-controller.enabled
