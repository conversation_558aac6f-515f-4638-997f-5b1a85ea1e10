# Default values for bk-nodeman.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

global:
  imageRegistry: ""
  imagePullSecrets: []
  storageClass: ""
  bkDomain: "example.com"
  ## 加密类型，默认值为 `CLASSIC`，可选项：`CLASSIC-国际密码算法`, `SHANGMI-国际商用算法`
  bkCryptoType: "CLASSIC"
  ## 蓝鲸主域名访问协议http/https
  bkDomainScheme: http

nameOverride: ""
fullnameOverride: ""

podAnnotations: {}

commonLabels: {}

commonAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

volumes: []

volumeMounts: []

affinity: {}

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

## 蓝鲸日志采集
##
bkLogConfig:
  enabled: false
  dataId: 1

## ServiceMonitor configuration
##
serviceMonitor:
  ## @param serviceMonitor.enabled Creates a ServiceMonitor to monitor kube-state-metrics
  ##
  enabled: false
  ## @param serviceMonitor.namespace Namespace in which Prometheus is running
  ## e.g:
  ## namespace: monitoring
  ##
  namespace: ""
  ## @param serviceMonitor.jobLabel The name of the label on the target service to use as the job name in prometheus.
  ##
  jobLabel: ""
  ## @param serviceMonitor.interval Scrape interval (use by default, falling back to Prometheus' default)
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
  ## e.g:
  ## interval: 10s
  ##
  interval: 60s
  ## @param serviceMonitor.scrapeTimeout Timeout after which the scrape is ended
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
  ## e.g:
  ## scrapeTimeout: 10s
  ##
  scrapeTimeout: ""
  ## @param serviceMonitor.selector ServiceMonitor selector labels
  ## ref: https://github.com/bitnami/charts/tree/master/bitnami/prometheus-operator#prometheus-configuration
  ## e.g:
  ## selector:
  ##   prometheus: my-prometheus
  ##
  selector: {}
  ## @param serviceMonitor.honorLabels Honor metrics labels
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
  ## e.g:
  ## honorLabels: false
  ##
  honorLabels: false
  ## @param serviceMonitor.relabelings ServiceMonitor relabelings
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#relabelconfig
  ##
  relabelings: []
  ## @param serviceMonitor.metricRelabelings ServiceMonitor metricRelabelings
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#relabelconfig
  ##
  metricRelabelings: []

images:
  saas:
    registry: "mirrors.tencent.com"
    repository: "blueking/bk-nodeman"
    pullPolicy: "IfNotPresent"
    tag: "2.2.8"

  backend:
    registry: "mirrors.tencent.com"
    repository: "blueking/bk-nodeman"
    pullPolicy: "IfNotPresent"
    tag: "2.2.8"

  busybox:
    registry: "docker.io"
    repository: "library/busybox"
    pullPolicy: "IfNotPresent"
    tag: "1.34.0"

  k8sWaitFor:
    registry: "docker.io"
    repository: "groundnuty/k8s-wait-for"
    pullPolicy: "IfNotPresent"
    tag: "v1.5.1"

  nginx:
    registry: "docker.io"
    repository: "library/nginx"
    pullPolicy: "IfNotPresent"
    tag: "1.20.2"


## Refer: https://github.com/bitnami/charts/tree/master/bitnami/nginx-ingress-controller/#installing-the-chart
nginx-ingress-controller:
  enabled: false
  ## 暴露 80 端口
  kind: "DaemonSet"
  daemonset:
    useHostPort: true
  defaultBackend:
    enabled: false
  service:
    type: "NodePort"


ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/proxy-body-size: "1024m"
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"

  hostname: "bknodeman.example.com"
  paths:
    - path: /
      pathType: ImplementationSpecific
    - path: /static/
      pathType: ImplementationSpecific
    - path: /backend/
      pathType: ImplementationSpecific
    - path: /core/api/gray/
      pathType: ImplementationSpecific

  ## @param ingress.selfSigned Create a TLS secret for this ingress record using self-signed certificates generated by Helm
  ##
  selfSigned: false

  ## @param ingress.tls Enable TLS configuration for the host defined at `ingress.hostname` parameter
  ## TLS certificates will be retrieved from a TLS secret with name: `{{- printf "%s-tls" .Values.ingress.hostname }}`
  ## You can:
  ##   - Use the `ingress.secrets` parameter to create this TLS secret
  ##   - Relay on cert-manager to create it by setting the corresponding annotations
  ##   - Relay on Helm to create self-signed certificates by setting `ingress.tls=true` and `ingress.certManager=false`
  ##
  tls: false

  ## @param ingress.extraPaths An array with additional arbitrary paths that may need to be added to the ingress under the main host
  ## e.g:
  ## extraPaths:
  ## - path: /*
  ##   backend:
  ##     serviceName: ssl-redirect
  ##     servicePort: use-annotation
  ##
  extraPaths: []

  ## @param ingress.extraTls TLS configuration for additional hostname(s) to be covered with this ingress record
  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls
  ## e.g:
  ## extraTls:
  ## - hosts:
  ##     - bknodeman.local
  ##   secretName: bknodeman.local-tls
  ##
  extraTls: []

  ## @param ingress.secrets Custom TLS certificates as secrets
  ## NOTE: 'key' and 'certificate' are expected in PEM format
  ## NOTE: 'name' should line up with a 'secretName' set further up
  ## If it is not set and you're using cert-manager, this is unneeded, as it will create a secret for you with valid certificates
  ## If it is not set and you're NOT using cert-manager either, self-signed certificates will be created valid for 365 days
  ## It is also possible to create and manage the certificates outside of this helm chart
  ## Please see README.md for more information
  ## e.g:
  ## secrets:
  ##   - name: bkiam.local-tls
  ##     key: |-
  ##       -----BEGIN RSA PRIVATE KEY-----
  ##       ...
  ##       -----END RSA PRIVATE KEY-----
  ##     certificate: |-
  ##       -----BEGIN CERTIFICATE-----
  ##       ...
  ##       -----END CERTIFICATE-----
  ##
  secrets: []

## --------------------------------------
## 第三方依赖，可被 config 中的同名变量覆盖
## --------------------------------------
## 蓝鲸 PaaS url（浏览器访问蓝鲸入口）
bkPaasUrl: "http://example.com"

## 蓝鲸 Login url（浏览器跳转登录用的URL前缀）
bkLoginUrl: "http://example.com/login"

## 蓝鲸 ESB url，注意集群内外都是统一域名。集群内可以配置域名解析到内网ip
bkComponentApiUrl: "http://bkapi.example.com"

## 节点管理浏览器访问地址
bkNodemanUrl: "http://bknodeman.example.com"
## 节点管理后台访问地址
bkNodemanApiUrl: "http://bk-nodeman-backend-api"

## 蓝鲸配置平台浏览器访问地址
bkCmdbUrl: http://cmdb.example.com

## 蓝鲸作业平台浏览器访问地址
bkJobUrl: "http://job.example.com"

## 蓝鲸权限中心 SaaS 地址
bkIamUrl: "http://bkiam.example.com"
## 蓝鲸权限中心后台 API 地址
bkIamApiUrl: "http://bkiam-api.example.com"

## 蓝鲸制品库浏览器访问域名和后台 API http://bkiam-api.example.com 域名同一个
bkRepoUrl: "http://bkrepo.example.com"

# 文档中心跳转链接
bkDocsCenterHost: ""

## --------------------------------------
## GSE 证书
## --------------------------------------
gseCert:

  ## 证书 CA 内容配置（base64）
  ca: ""

  ## Server 侧 CERT 内容配置（base64）
  cert: ""

  ## Server 侧 KEY 内容配置（base64）
  key: ""

  ## 证书密码文件内容配置, 用于解密证书密码
  certEncryptKey: ""

  ## API 侧 CERT
  ##
  apiClient:

    ## API 侧 CERT 内容配置, 用于其他服务调用 GSE（base64）
    cert: ""

    ## API 侧 KEY 内容配置, 用于其他服务调用 GSE（base64）
    key: ""

  ## Agent 侧 CERT
  ##
  agent:

    ## Agent 侧 CERT 内容配置, 用于 Agent 链路（base64）
    cert: ""

    ## Agent 侧 KEY 内容配置, 用于 Agent 链路（base64）
    key: ""


## --------------------------------------
## 节点管理系统配置
## --------------------------------------
config:
  ## 应用认证
  ##
  appCode: "bk_nodeman"
  appSecret: ""

  ## 运行时
  ##
  ## 运行环境，ce / ee / ieod，设置为 ce 将会改变 gse 端口的默认配置
  bkAppRunEnv: "ce"
  ## 开发框架 PaaS 版本适配，目前仅支持 `3`
  bkPaasMajorVersion: 3
  ## 开发框架 PaaS 环境适配，目前仅支持 `prod`
  bkPaaSEnvironment: "prod"

  ## 日志
  ## 日志类别，DEFAULT / STDOUT
  logType: "STDOUT"
  ## 日志级别
  logLevel: "INFO"
  ## 日志所在目录
  bkLogDir: "/data/bkee/logs/bknodeman"

  ## 缓存
  ## 缓存后端，默认值为 `db`，可选项：`db`、`redis` - 仅存在 `REDIS_HOST` 变量时生效，否则仍默认使用 `db`
  cacheBackend: "db"
  ## 是否预热关键缓存，一般在切换缓存前需要开启
  cacheEnablePreheat: false

  ## 蓝鲸配置平台
  ##
  ## 资源池 ID
  bkCmdbResourcePoolBizId: 1
  ## 企业账户
  defaultSupplierAccount: "0"

  ## 蓝鲸作业平台
  ##
  ## API 版本，可选项 `V2` `V3`
  jobVersion: "V3"
  ## 业务集 ID
  bluekingBizId: 9991001
  ## 蓝鲸作业平台浏览器访问地址
  bkJobUrl: ""

  ## 蓝鲸权限中心
  ##
  ## 是否使用权限中心
  bkAppUseIam: true
  bkIamV3AppCode: "bk_iam"
  ## 权限中心拉取权限相关资源的访问地址，默认取 .Values.bkNodemanUrl
  bkAppIamResourceApiHost: ""

  ## 组件 API 接入地址
  ##
  ## 节点管理 API 访问地址，用于覆盖 bkComponentApiUrl 访问节点管理
  bkAppBkNodeApiGateway: ""
  ## 管控平台 API 访问地址，用于覆盖 bkComponentApiUrl 访问管控平台 API
  bkAppBkGseApiGateway: ""

  ## 节点管理自身模块依赖
  ##
  ## 节点管理后台访问地址，用于自身文件服务，默认取 .Values.bkNodemanApiUrl
  bkAppBackendHost: ""
  ## 节点管理后台内网回调地址，默认通过 .Values.ingress.hostname & .Values.global.bkDomainScheme 拼接
  bkAppNodemanCallbackUrl: ""
  ## 节点管理后台外网回调地址，默认通过 .Values.ingress.hostname & .Values.global.bkDomainScheme 拼接
  bkAppNodemanOuterCallbackUrl: ""

  ## 蓝鲸管控平台 Agent，AgentXXDir 仅在初次部署有效，后续可以在页面「全局配置」维护
  ##
  ## 平台版本，默认为 `V1`,可选：`V1` `V2`
  gseVersion: "V1"
  ## GSE 本地证书路径，渲染时为空默认取 `/data/bk{{ .Values.config.bkAppRunEnv }}/cert`
  gseCertPath: ""
  ## 是否启用推送 GSE 环境变量文件，如果启用，将在 Agent `安装`/`重装`/`重载配置`/`灰度` 操作成功后，进行如下操作：
  ## Windows：推送 `environ.sh` & `environ.bat` 到目标机器的 `GSE_ENVIRON_WIN_DIR` 路径
  ## Linux：推送 `environ.sh` 到目标机器的 `GSE_ENVIRON_DIR` 路径
  gseEnablePushEnvironFile: false
  ## GSE 环境变量目录
  gseEnvironDir: "/etc/sysconfig/gse/bk"
  ## GSE 环境变量目录（Windows）
  gseEnvironWinDir: "C:\\\\Windows\\\\System32\\\\config\\\\gse\\\\bk"
  ## 是否启用 GSE 服务探测，为 `true` 将定期更新默认接入点的 gse svr 信息
  gseEnableSvrDisCovery: true
  ## ZK hosts 信息，host:port，多个 hosts 以 `,` 分隔
  bkAppGseZkHost: "127.0.0.1:2181"
  ## ZK 认证信息，用户名:密码
  bkAppGseZkAuth: "bkzk:zkpass"
  ## Linux Agent 安装目录
  bkAppGseAgentHome: "/usr/local/gse"
  ## Linux Agent 日志目录
  bkAppGseAgentLogDir: "/var/log/gse"
  ## Linux Agent 运行目录
  bkAppGseAgentRunDir: "/var/run/gse"
  ## Linux Agent 数据目录
  bkAppGseAgentDataDir: "/var/lib/gse"
  ## Windows Agent 安装目录
  bkAppGseWinAgentHome: "C:\\\\gse"
  ## Windows Agent 日志目录
  bkAppGseWinAgentLogDir: "C:\\\\gse\\\\logs"
  ## Windows Agent 运行目录
  bkAppGseWinAgentRunDir: "C:\\\\gse\\\\data"
  ## Windows Agent 数据目录
  bkAppGseWinAgentDataDir: "C:\\\\gse\\\\data"

  ## 存储
  ##
  storageType: "BLUEKING_ARTIFACTORY"
  ## 文件服务器内网IP，用于物理机文件分发，如果 storageType=BLUEKING_ARTIFACTORY，该值是非必填的
  lanIp: "127.0.0.1"
  ## 文件存储目录
  bkAppPublicPath: "/data/bkee/public/bknodeman/"
  bkRepoProject: ""
  bkRepoPassword: ""
  bkRepoUsername: ""
  bkRepoBucket: ""
  bkRepoPublicBucket: ""
  bkRepoPrivateBucket: ""

  ## 可观测
  ##
  ## 是否开启 Trace
  bkAppEnableOtelTrace: false
  ## 是否开启 DB 访问 trace（开启后 span 数量会明显增多）
  bkAppOtelInstrumentDbApi: false
  ## 配置采样策略，默认值为 `parentbased_always_off`，可选值 `always_on`，`always_off`, `parentbased_always_on`,
  ## `parentbased_always_off`, `traceidratio`, `parentbased_traceidratio`
  bkAppOtelSampler: "parentbased_always_off"
  ## 监控上报配置项
  bkAppOtelBkDataToken: ""
  bkAppOtelGrpcUrl: ""

  ## 是否启用自定义上报
  bkAppMonitorReporterEnable: false
  ## 监控 Data ID
  bkAppMonitorReporterDataId: 0
  ## 自定义上报 Token
  bkAppMonitorReporterAccessToken: ""
  ## 上报唯一标志符
  bkAppMonitorReporterTarget: "prod"
  ## 上报地址
  bkAppMonitorReporterUrl: ""
  ## 上报间隔
  bkAppMonitorReporterInterval: 10
  ## 块大小
  bkAppMonitorReporterChunkSize: 200

  ## 导航栏开源社区地址
  bkAppNavOpenSourceUrl: "https://github.com/TencentBlueKing/bk-nodeman"
  ## 导航栏技术支持地址
  bkAppNavHelperUrl: "https://wpa1.qq.com/KziXGWJs?_type=wpa&qidian=true"

  ## 其他
  ##
  ## 线程最大并发数
  concurrentNumber: 50
  bkAppEnableDHCP: false
  ## 插件进程状态同步周期
  bkAppSyncProcStatusTaskInterval: 1200
  ## Agent安装前置脚本
  bkAppScriptHooks: ""
  ## WINDOWS IEOD脚本内容
  bkAppIEODActiveFirewallPolicyScriptInfo: ""
  ## 自动选择安装通道ID
  bkAppDefaultInstallChannelId: -1
  ## 自动选择安装通道对应云区域ID
  bkAppAutomaticChoiceCloudId: -1
  ## 未分配管控区域ID
  bkAppUnassignedCloudId: 90000001


## --------------------------------------
## 节点管理后台环境变量配置
## --------------------------------------
backendConfig: {}

## --------------------------------------
## 额外的环境变量，可用于覆盖内置环境变量
## 优先级：内置环境变量 < extraEnvVarsCM < extraEnvVarsSecret < extraEnvVars < backendExtraEnvVars (仅后台)
## --------------------------------------
extraEnvVars: []
extraEnvVarsCM: ""
extraEnvVarsSecret: ""
backendExtraEnvVars: []

migrateJob:
  db:
    resources: {}
  fileSync:
    resources: {}

saas:
  enabled: true
  api:
    enabled: true
    service:
      type: "ClusterIP"
      port: 10300
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/start-saas-api"
    timeout: 30
    workerNum: 4
    ## 预留暂未使用
    threadNum: 4

  web:
    enabled: true
    service:
      type: "ClusterIP"
      port: 80
    resources: {}
    replicaCount: 1

backend:
  enabled: true
  miniDeploy: false
  api:
    enabled: true
    service:
      type: "NodePort"
      port: 10300
      nodePort: 30300
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/start-backend-api"
    timeout: 60
    keepAlive: "0"
    workerNum: 1
    threadNum: 3

  celeryBeat:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-beat"

  ## 仅在 backend.miniDeploy=true 时启用
  commonWorker:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-common-worker"

  ## 仅在 backend.miniDeploy=true 时启用
  commonPipelineWorker:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-common-pipeline-worker"

  dworker:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-dworker"

  bworker:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-bworker"

  baworker:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-baworker"

  pworker:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-pworker"

  psworker:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-psworker"

  paworker:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "bin/hooks/start_cmds/celery/start-paworker"

  syncHost:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "python manage.py sync_host_event"

  syncHostRe:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "python manage.py sync_host_relation_event"

  syncProcess:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "python manage.py sync_process_event"

  resourceWatch:
    enabled: true
    resources: {}
    replicaCount: 1
    command: "python manage.py apply_resource_watched_events"


## Redis chart configuration
# https://github.com/bitnami/charts/blob/master/bitnami/redis/README.md
##
redis:
  enabled: true
  architecture: "standalone"

  auth:
    enabled: true
    password: "bk_nodeman"

  master:
    service:
      port: 6379

    persistence:
      enabled: true
      storageClass: ""
      size: "8Gi"


## MySQL configuration
# https://github.com/bitnami/charts/blob/master/bitnami/mysql/README.md
##
mysql:
  enabled: true
  architecture: "standalone"

  auth:
    rootPassword: "bk_nodeman"
    database: "bk_nodeman"
    username: "bk_nodeman"
    password: "bk_nodeman"

  initdbScripts:
    grant_user_pms.sql: |
      grant all privileges on *.* to 'bk_nodeman'@'%';
      flush privileges;

  primary:
    service:
      port: 3306

    persistence:
      enabled: true
      storageClass: ""
      size: "8Gi"

    configuration: |-
      [mysqld]
      default_authentication_plugin=mysql_native_password
      skip-name-resolve
      explicit_defaults_for_timestamp
      basedir=/opt/bitnami/mysql
      plugin_dir=/opt/bitnami/mysql/lib/plugin
      port=3306
      socket=/opt/bitnami/mysql/tmp/mysql.sock
      datadir=/bitnami/mysql/data
      tmpdir=/opt/bitnami/mysql/tmp
      max_allowed_packet=64M
      bind-address=0.0.0.0
      pid-file=/opt/bitnami/mysql/tmp/mysqld.pid
      log-error=/opt/bitnami/mysql/logs/mysqld.log

      # change charset
      collation-server=utf8mb4_unicode_ci
      init-connect='SET NAMES utf8mb4'
      character-set-server=utf8mb4

      [client]
      port=3306
      socket=/opt/bitnami/mysql/tmp/mysql.sock
      # change charset
      default-character-set=utf8mb4
      plugin_dir=/opt/bitnami/mysql/lib/plugin
      [manager]
      port=3306
      socket=/opt/bitnami/mysql/tmp/mysql.sock
      pid-file=/opt/bitnami/mysql/tmp/mysqld.pid



## RabbitMQ chart configuration
## https://github.com/bitnami/charts/blob/master/bitnami/rabbitmq/values.yaml
##
rabbitmq:
  enabled: true
  auth:
    username: "bk_nodeman"
    password: "bk_nodeman"

  service:
    port: 5672

  persistence:
    enabled: true
    storageClass: ""
    size: "8Gi"

  extraConfiguration: |-
    default_vhost = bk-nodeman
    default_permissions.configure = .*
    default_permissions.read = .*
    default_permissions.write = .*



## External Redis configuration
##
externalRedis:
  architecture: "standalone"
  host: "bk_nodeman"
  port: 6379
  password: "bk_nodeman"

## External MySQL configuration
##
externalMySQL:
  host: "host.docker.internal"
  port: 3306
  username: "bk_nodeman"
  password: "bk_nodeman"
  database: "bk_nodeman"

## External RabbitMQ configuration
##
externalRabbitMQ:
  host: "host.docker.internal"
  port: 5672
  vhost: "bk_nodeman"
  username: "bk_nodeman"
  password: "bk_nodeman"

bkNotice:
  enabled: false
