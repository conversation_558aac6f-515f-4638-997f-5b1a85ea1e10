{{- if and .Values.backend.enabled .Values.backend.dworker.enabled (not .Values.backend.miniDeploy) -}}
{{- $moduleConf := .Values.backend.dworker -}}
{{- $fullName := ( include "bk-nodeman.backend-dworker.fullname" .) -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ $fullName }}"
  labels:
    {{- include "bk-nodeman.labels" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ $moduleConf.replicaCount | default 1 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "bk-nodeman.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: "{{ $fullName }}"
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "bk-nodeman.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: "{{ $fullName }}"
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bk-nodeman.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      {{- include "bk-nodeman.backend.initContainers" . | nindent 6 }}
      containers:
        - name: {{ $fullName }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.global.imageRegistry | default .Values.images.backend.registry }}/{{ .Values.images.backend.repository }}:{{ .Values.images.backend.tag | default .Chart.AppVersion }}"
          imagePullPolicy: "{{ .Values.images.backend.pullPolicy }}"
          command: ["/bin/bash", "-c"]
          args:
            {{- if $moduleConf.command }}
            - "{{ $moduleConf.command }}"
            {{- else }}
            - "bin/hooks/start_cmds/celery/start-dworker"
            {{- end }}
          volumeMounts:
            {{- include "bk-nodeman.volumeMounts" . | nindent 12 }}
          {{- include "bk-nodeman.backend.env" . | nindent 10 }}
          resources:
            {{- toYaml $moduleConf.resources | nindent 12 }}
      volumes:
        {{- include "bk-nodeman.volumes" . | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end -}}
