{{- $fullName := printf "%s-%s" (include "bk-nodeman.fullname" .) "backend-env-configmap" -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "bk-nodeman.labels" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  BK_BACKEND_CONFIG: "true"

  BACKEND_API_PORT: "{{ .Values.backend.api.service.port | default 10300 }}"
  GUNICORN_TIMEOUT: "{{ .Values.backend.api.timeout | default 30 }}"
  GUNICORN_WORKER_NUM: "{{ .Values.backend.api.workerNum | default 1 }}"
  GUNICORN_THREAD_NUM: "{{ .Values.backend.api.threadNum | default 3 }}"

  {{- if .Values.backend.api.keepAlive }}
  GUNICORN_KEEP_ALIVE: "{{ .Values.backend.api.keepAlive }}"
  {{- end }}

  {{- if .Values.config.bkAppEnableOtelTrace }}
  BKAPP_OTEL_SERVICE_NAME: {{ printf "%s-%s" (include "bk-nodeman.fullname" .) "backend" }}
  {{- end }}
