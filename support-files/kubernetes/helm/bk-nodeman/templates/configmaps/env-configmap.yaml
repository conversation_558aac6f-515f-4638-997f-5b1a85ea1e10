{{- $fullName := printf "%s-%s" (include "bk-nodeman.fullname" .) "env-configmap" -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: "{{ $fullName }}"
  labels:
    {{- include "bk-nodeman.labels" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  APP_ID: "{{ .Values.config.appCode }}"
  APP_TOKEN: "{{ .Values.config.appSecret }}"
  BKPAAS_APP_ID: "{{ .Values.config.appCode }}"
  BKPAAS_APP_CODE: "{{ .Values.config.appCode }}"
  BKPAAS_APP_SECRET: "{{ .Values.config.appSecret }}"

  BKAPP_RUN_ENV: "{{ .Values.config.bkAppRunEnv }}"
  BKAPP_ENABLE_DHCP: "{{ .Values.config.bkAppEnableDHCP }}"
  BKPAAS_MAJOR_VERSION: "{{ .Values.config.bkPaasMajorVersion }}"
  BKPAAS_ENVIRONMENT: "{{ .Values.config.bkPaaSEnvironment }}"

  LOG_TYPE: "{{ .Values.config.logType }}"
  LOG_LEVEL: "{{ .Values.config.logLevel }}"
  BK_LOG_DIR: "{{ .Values.config.bkLogDir }}"

  CACHE_BACKEND: "{{ .Values.config.cacheBackend }}"
  CACHE_ENABLE_PREHEAT: "{{ .Values.config.cacheEnablePreheat }}"

  BK_CMDB_RESOURCE_POOL_BIZ_ID: "{{ .Values.config.bkCmdbResourcePoolBizId }}"
  DEFAULT_SUPPLIER_ACCOUNT: "{{ .Values.config.defaultSupplierAccount }}"

  JOB_VERSION: "{{ .Values.config.jobVersion }}"
  BLUEKING_BIZ_ID: "{{ .Values.config.bluekingBizId }}"
  BK_CC_HOST: "{{ .Values.bkCmdbUrl }}"
  BK_JOB_HOST: "{{ .Values.config.bkJobUrl | default .Values.bkJobUrl }}"

  BKAPP_USE_IAM: "{{ .Values.config.bkAppUseIam }}"
  BK_IAM_V3_APP_CODE: "{{ .Values.config.bkIamV3AppCode }}"
  BK_IAM_V3_SAAS_HOST: "{{ .Values.bkIamUrl }}"
  BK_IAM_V3_INNER_HOST: "{{ .Values.bkIamApiUrl }}"
  BKAPP_IAM_RESOURCE_API_HOST: "{{ .Values.config.bkAppIamResourceApiHost | default .Values.bkNodemanUrl }}"

  BK_PAAS_HOST: "{{ .Values.bkPaasUrl }}"
  BK_PAAS2_URL: "{{ default .Values.bkPaasUrl }}"
  BK_LOGIN_URL: "{{ .Values.bkLoginUrl }}"

  BK_PAAS_INNER_HOST: "{{ .Values.bkComponentApiUrl }}"
  BK_PAAS_OUTER_HOST: "{{ .Values.bkComponentApiUrl }}"
  {{- if .Values.config.bkAppBkNodeApiGateway }}
  BKAPP_BK_NODE_APIGATEWAY: "{{ .Values.config.bkAppBkNodeApiGateway }}/"
  {{- end }}
  {{- if .Values.config.bkAppBkGseApiGateway }}
  BKAPP_BK_GSE_APIGATEWAY: "{{ .Values.config.bkAppBkGseApiGateway }}/"
  {{- end }}

  BK_NODEMAN_URL: "{{ .Values.bkNodemanUrl }}"
  BKAPP_BACKEND_HOST: "{{ .Values.config.bkAppBackendHost | default .Values.bkNodemanApiUrl }}"
  BKAPP_NODEMAN_CALLBACK_URL: "{{ .Values.config.bkAppNodemanCallbackUrl | default ( printf "%s/%s" .Values.bkNodemanUrl "backend" ) }}"
  BKAPP_NODEMAN_OUTER_CALLBACK_URL: "{{ .Values.config.bkAppNodemanOuterCallbackUrl | default ( printf "%s/%s" .Values.bkNodemanUrl "backend" ) }}"

  GSE_VERSION: "{{ .Values.config.gseVersion }}"
  GSE_CERT_PATH: "{{ include "bk-nodeman.env.gseCertPath" . }}"
  GSE_ENABLE_PUSH_ENVIRON_FILE: "{{ .Values.config.gseEnablePushEnvironFile }}"
  GSE_ENVIRON_DIR: "{{ .Values.config.gseEnvironDir }}"
  GSE_ENVIRON_WIN_DIR: "{{ .Values.config.gseEnvironWinDir }}"
  GSE_ENABLE_SVR_DISCOVERY: "{{ .Values.config.gseEnableSvrDisCovery }}"
  BKAPP_GSE_ZK_HOST: "{{ .Values.config.bkAppGseZkHost }}"
  BKAPP_GSE_ZK_AUTH: "{{ .Values.config.bkAppGseZkAuth }}"
  BKAPP_GSE_AGENT_HOME: "{{ .Values.config.bkAppGseAgentHome }}"
  BKAPP_GSE_AGENT_LOG_DIR: "{{ .Values.config.bkAppGseAgentLogDir }}"
  BKAPP_GSE_AGENT_RUN_DIR: "{{ .Values.config.bkAppGseAgentRunDir}}"
  BKAPP_GSE_AGENT_DATA_DIR: "{{ .Values.config.bkAppGseAgentDataDir }}"
  BKAPP_GSE_WIN_AGENT_HOME: "{{ .Values.config.bkAppGseWinAgentHome }}"
  BKAPP_GSE_WIN_AGENT_LOG_DIR: "{{ .Values.config.bkAppGseWinAgentLogDir }}"
  BKAPP_GSE_WIN_AGENT_RUN_DIR: "{{ .Values.config.bkAppGseWinAgentRunDir }}"
  BKAPP_GSE_WIN_AGENT_DATA_DIR: "{{ .Values.config.bkAppGseWinAgentDataDir }}"

  STORAGE_TYPE: "{{ .Values.config.storageType }}"
  {{- if .Values.config.lanIp }}
  LAN_IP: "{{ .Values.config.lanIp }}"
  {{- end }}
  BKAPP_PUBLIC_PATH: "{{ .Values.config.bkAppPublicPath }}"
  BKREPO_PROJECT: "{{ .Values.config.bkRepoProject }}"
  BKREPO_PASSWORD: "{{ .Values.config.bkRepoPassword }}"
  BKREPO_USERNAME: "{{ .Values.config.bkRepoUsername }}"
  BKREPO_BUCKET: "{{ .Values.config.bkRepoBucket }}"
  BKREPO_PUBLIC_BUCKET: "{{ .Values.config.bkRepoPublicBucket }}"
  BKREPO_PRIVATE_BUCKET: "{{ .Values.config.bkRepoPrivateBucket }}"
  BKREPO_ENDPOINT_URL: "{{ .Values.bkRepoUrl }}"

  {{- if .Values.config.bkAppEnableOtelTrace }}
  BKAPP_ENABLE_OTEL_TRACE: "{{ .Values.config.bkAppEnableOtelTrace }}"
  BKAPP_OTEL_SERVICE_NAME: {{ printf "%s-%s" (include "bk-nodeman.fullname" .) "saas" }}
  BKAPP_OTEL_INSTRUMENT_DB_API: "{{ .Values.config.bkAppOtelInstrumentDbApi }}"
  BKAPP_OTEL_SAMPLER: "{{ .Values.config.bkAppOtelSampler }}"
  BKAPP_OTEL_BK_DATA_TOKEN: "{{ .Values.config.bkAppOtelBkDataToken }}"
  BKAPP_OTEL_GRPC_URL: "{{ .Values.config.bkAppOtelGrpcUrl }}"
  {{- end }}

  {{- if .Values.config.bkAppMonitorReporterEnable }}
  BKAPP_MONITOR_REPORTER_ENABLE: "{{ .Values.config.bkAppMonitorReporterEnable }}"
  BKAPP_MONITOR_REPORTER_DATA_ID: "{{ int .Values.config.bkAppMonitorReporterDataId }}"
  BKAPP_MONITOR_REPORTER_ACCESS_TOKEN: "{{ .Values.config.bkAppMonitorReporterAccessToken }}"
  BKAPP_MONITOR_REPORTER_TARGET: "{{ .Values.config.bkAppMonitorReporterTarget }}"
  BKAPP_MONITOR_REPORTER_URL: "{{ .Values.config.bkAppMonitorReporterUrl }}"
  BKAPP_MONITOR_REPORTER_REPORT_INTERVAL: "{{ .Values.config.bkAppMonitorReporterInterval }}"
  BKAPP_MONITOR_REPORTER_CHUNK_SIZE: "{{ .Values.config.bkAppMonitorReporterChunkSize }}"
  {{- end }}

  CONCURRENT_NUMBER: "{{ .Values.config.concurrentNumber }}"

  SAAS_API_PORT: "{{ .Values.saas.api.service.port }}"
  GUNICORN_TIMEOUT: "{{ .Values.saas.api.timeout | default 30 }}"
  GUNICORN_WORKER_NUM: "{{ .Values.saas.api.workerNum | default 4 }}"
  GUNICORN_THREAD_NUM: "{{ .Values.saas.api.threadNum | default 4 }}"

  BKAPP_NAV_OPEN_SOURCE_URL: "{{ .Values.config.bkAppNavOpenSourceUrl }}"
  BKAPP_NAV_HELPER_URL: "{{ .Values.config.bkAppNavHelperUrl }}"
  BKPAAS_BK_DOMAIN: "{{ .Values.global.bkDomain }}"
  BKPAAS_BK_CRYPTO_TYPE: "{{ .Values.global.bkCryptoType | default "CLASSIC" }}"
  BK_DOCS_CENTER_HOST: "{{ .Values.bkDocsCenterHost }}"

  BKAPP_SYNC_PROC_STATUS_TASK_INTERVAL: "{{ .Values.config.bkAppSyncProcStatusTaskInterval }}"

  BKAPP_SCRIPT_HOOKS: "{{ .Values.config.bkAppScriptHooks }}"
  BKPAAS_SHARED_RES_URL: "{{ .Values.bkSharedResUrl }}"
  BKAPP_LEGACY_AUTH: "{{ .Values.config.bkAppLegacyAuth }}"
  BK_NOTICE_ENABLED: "{{ .Values.bkNotice.enabled }}"
  BKAPP_IEOD_ACTIVE_FIREWALL_POLICY_SCRIPT_INFO: '{{ .Values.config.bkAppIEODActiveFirewallPolicyScriptInfo }}'
  BKAPP_DEFAULT_INSTALL_CHANNEL_ID: "{{ .Values.config.bkAppDefaultInstallChannelId}}"
  BKAPP_AUTOMATIC_CHOICE_CLOUD_ID: "{{ .Values.config.bkAppAutomaticChoiceCloudId}}"
  TXY_ENDPOINT: "{{ .Values.config.TXYEndpoint }}"
  TXY_SECRETID: "{{ .Values.config.TXYSecretId }}"
  TXY_SECRETKEY: "{{ .Values.config.TXYSecretKey }}"
  BKAPP_UNASSIGNED_CLOUD_ID: "{{ .Values.config.bkAppUnassignedCloudId}}"
