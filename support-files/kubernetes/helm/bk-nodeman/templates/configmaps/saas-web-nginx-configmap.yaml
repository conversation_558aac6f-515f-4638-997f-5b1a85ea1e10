{{- if and .Values.saas.enabled .Values.saas.web.enabled -}}
{{- $moduleConf := .Values.saas.web -}}
{{- $svcPort := $moduleConf.service.port | default 80 -}}
{{- $fullName := printf "%s-%s" (include "bk-nodeman.saas-web.fullname" .) "nginx-configmap" -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "bk-nodeman.labels" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  nginx.conf: |
    server {

        listen {{ $svcPort }};
        listen [::]:{{ $svcPort }};
        server_name _;

        location /static/ {
            alias /app/staticfiles/;
        }
    }
{{- end -}}
