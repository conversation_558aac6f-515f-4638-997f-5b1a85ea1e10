Thank you for installing {{ .Chart.Name }}.

Your release is named {{ .Release.Name }}.

如果集群中已经安装了 IngressController，那么可以通过以下地址访问节点管理：
{{- if .Values.ingress.hostname }}
export BK_NODEMAN_URL="{{ .Values.global.bkDomainScheme }}://{{ .Values.ingress.hostname }}"
SaaS: {{ .Values.global.bkDomainScheme }}://{{ .Values.ingress.hostname }}
{{- else }}
export BK_NODEMAN_URL="{{ .Values.global.bkDomainScheme }}://bknodeman.{{ .Values.global.bkDomain }}"
SaaS: {{ .Values.global.bkDomainScheme }}://bknodeman.{{ .Values.global.bkDomain }}
{{- end }}

验证节点管理后台可访问：
{{- if eq .Values.backend.api.service.type "NodePort" }}
    export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ include "bk-nodeman.backend-api.fullname" . }})
    export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
    curl $NODE_IP:$NODE_PORT/backend/version/

{{- else if eq .Values.backend.api.service.type "ClusterIP" }}
- 通过 port-forward 暴露 {{ include "bk-nodeman.backend-api.fullname" . }}
    kubectl port-forward --namespace {{ .Release.Namespace }} service/{{ include "bk-nodeman.backend-api.fullname" . }} {{ .Values.backend.api.service.port }}:{{ .Values.backend.api.service.port }}
{{- end }}

查看更多信息:
  $ kubectl get pod -n {{ .Release.Namespace }} -o wide
  $ kubectl get svc -n {{ .Release.Namespace }} -o wide


更多实用性操作:

- 获取第一个存活 POD NAME

# 后续操作依赖该变量
export FIRST_RUNNING_POD=$(kubectl get pods -n {{ .Release.Namespace }} \
  --selector=app.kubernetes.io/instance={{ .Release.Name }} --field-selector=status.phase=Running \
  -o custom-columns=":metadata.name" | sed '/^$/d' | head -n 1 )


- 导入插件包

# 将插件包上传到容器指定目录，可以重复该步骤，导入多个插件包
kubectl cp <插件包本地路径> -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD}:/app/official_plugin/

# 解析并导入插件包
kubectl exec -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD} -- python manage.py init_official_plugins

- 导入 2.0 Agent/Proxy 包

# 删除缓存的包
kubectl exec -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD} -- sh -c 'rm -f /app/official_plugin/gse_agent/* && rm -f /app/official_plugin/gse_proxy/*'

# [Agent] 将 client 包上传到容器指定目录，可以重复该步骤，导入多个版本的 client 包，client 包一般格式为：gse_agent_ce-2.0.0.tgz
kubectl cp <client包本地路径> -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD}:/app/official_plugin/gse_agent/

# [Proxy] 将 server 包上传到容器指定目录，可以重复该步骤，导入多个版本的 server 包，server 包一般格式为：gse_ce-2.0.0.tgz
# 注意：导入 server 包依赖同版本的 client 包，请确保 client 包已上传或已导入
kubectl cp <client包本地路径> -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD}:/app/official_plugin/gse_proxy/

# 解析并导入 Agent/Proxy
# -o --overwrite_version 版本号，用于覆盖原始制品内的版本信息，`stable` 为内置版本
# 修改内置版本：${BK_NODEMAN_URL}/admin_nodeman/node_man/globalsettings/ 新建或修改 `GSE_AGENT2_VERSION`，默认为 `"stable"`
kubectl exec -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD} -- python manage.py init_agents -o stable


- 同步主机相关数据

# 节点管理 backend 模块下 syncHost & syncHostRe & syncProcess & resourceWatch 负责实时监听主机变更事件并同步
# 理论上主机数据无需手动同步，但部分特殊使用场景下可能需要：
# - backend.celeryBeat / backend.commonWorker / backend.dworker 未启用
# - 更换 DB 实例
# - ...

# 同步主机
kubectl exec -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD} -- python manage.py sync_cmdb_host

# 同步主机 Agent 状态
kubectl exec -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD} -- python manage.py sync_agent_status

# 同步主机插件进程状态
kubectl exec -n {{ .Release.Namespace }} ${FIRST_RUNNING_POD} -- python manage.py sync_plugin_status
