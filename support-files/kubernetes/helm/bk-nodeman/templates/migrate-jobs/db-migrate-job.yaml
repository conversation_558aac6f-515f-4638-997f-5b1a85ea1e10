apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ include "bk-nodeman.migrate-job.db" . }}"
  labels:
    {{- include "bk-nodeman.labels" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  backoffLimit: 10
  template:
    metadata:
      labels:
        {{- include "bk-nodeman.labels" . | nindent 8 }}
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bk-nodeman.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      restartPolicy: OnFailure
      initContainers:
        - name: check-database-ready
          image: "{{ .Values.global.imageRegistry | default .Values.images.busybox.registry }}/{{ .Values.images.busybox.repository }}:{{ .Values.images.busybox.tag }}"
          imagePullPolicy: "{{ .Values.images.busybox.pullPolicy }}"
          command: ["sh", "-c"]
          args:
            - "echo Start check database: $(MYSQL_HOST):$(MYSQL_PORT); until telnet $(MYSQL_HOST) $(MYSQL_PORT); do echo waiting for db $(MYSQL_HOST):$(MYSQL_PORT); sleep 2; done;"
          {{- include "bk-nodeman.saas.env" . | nindent 10 }}
          resources:
            {{- toYaml .Values.migrateJob.db.resources | nindent 12 }}
      containers:
        - name: "{{ include "bk-nodeman.migrate-job.db" . }}"
          image: "{{ .Values.global.imageRegistry | default .Values.images.saas.registry }}/{{ .Values.images.saas.repository }}:{{ .Values.images.saas.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.images.saas.pullPolicy }}
          command: ["/bin/bash", "-c"]
          args:
            - "bin/hooks/migrate-db"
          {{- include "bk-nodeman.saas.env" . | nindent 10 }}
          volumeMounts:
            {{- include "bk-nodeman.volumeMounts" . | nindent 12 }}
          resources:
            {{- toYaml .Values.migrateJob.db.resources | nindent 12 }}
      volumes:
        {{- include "bk-nodeman.volumes" . | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
