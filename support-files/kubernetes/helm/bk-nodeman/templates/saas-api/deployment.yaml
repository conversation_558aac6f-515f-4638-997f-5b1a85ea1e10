{{- if and .Values.saas.enabled .Values.saas.api.enabled -}}
{{- $moduleConf := .Values.saas.api -}}
{{- $svcPort := $moduleConf.service.port | default 10300 -}}
{{- $fullName := ( include "bk-nodeman.saas-api.fullname" .) -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: "{{ $fullName }}"
  labels:
    {{- include "bk-nodeman.labels" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ $moduleConf.replicaCount | default 1 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "bk-nodeman.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: "{{ $fullName }}"
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "bk-nodeman.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: "{{ $fullName }}"
    spec:
      {{- with .Values.global.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bk-nodeman.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: "{{ include "bk-nodeman.migrate-job.db.pure" . }}"
          image: "{{ .Values.global.imageRegistry | default .Values.images.k8sWaitFor.registry }}/{{ .Values.images.k8sWaitFor.repository }}:{{ .Values.images.k8sWaitFor.tag }}"
          imagePullPolicy: "{{ .Values.images.k8sWaitFor.pullPolicy }}"
          args: ["job", "{{ include "bk-nodeman.migrate-job.db" . }}"]
          resources:
            {{- toYaml $moduleConf.resources | nindent 12 }}
      containers:
        - name: {{ $fullName }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.global.imageRegistry | default .Values.images.saas.registry }}/{{ .Values.images.saas.repository }}:{{ .Values.images.saas.tag | default .Chart.AppVersion }}"
          imagePullPolicy: "{{ .Values.images.saas.pullPolicy }}"
          command: ["/bin/bash", "-c"]
          args:
            {{- if $moduleConf.command }}
            - "{{ $moduleConf.command }}"
            {{- else }}
            - "bin/hooks/start_cmds/start-saas-api"
            {{- end }}
          volumeMounts:
            {{- include "bk-nodeman.volumeMounts" . | nindent 12 }}
          {{- include "bk-nodeman.saas.env" . | nindent 10 }}
          ports:
            - name: http
              containerPort: {{ $svcPort }}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /version
              port: {{ $svcPort }}
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 10
            successThreshold: 1
          readinessProbe:
            httpGet:
              path: /version
              port: {{ $svcPort }}
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 15
            successThreshold: 1
          resources:
            {{- toYaml $moduleConf.resources | nindent 12 }}
      volumes:
        {{- include "bk-nodeman.volumes" . | nindent 8 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end -}}
