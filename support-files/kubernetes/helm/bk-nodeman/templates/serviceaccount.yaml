{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "bk-nodeman.fullname" . }}
  labels:
    {{- include "bk-nodeman.labels" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
    {{- end }}
    {{- range $key, $value := .Values.serviceAccount.annotations }}
    {{ $key }}: {{ include "common.tplvalues.render" (dict "value" $value "context" $) | quote }}
    {{- end }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: bk-nodeman-role
rules:
- apiGroups:
  - batch
  resources:
  - jobs
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: bk-nodeman-role-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: bk-nodeman-role
subjects:
- kind: ServiceAccount
  name: {{ include "bk-nodeman.fullname" . }}
  namespace: {{ default "default" .Release.Namespace }}
{{- end }}
