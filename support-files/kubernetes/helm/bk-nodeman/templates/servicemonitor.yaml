{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: "{{ include "bk-nodeman.fullname" . }}-servicemonitor"
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "bk-nodeman.labels" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- with .Values.commonAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.serviceMonitor.jobLabel }}
  jobLabel: {{ .Values.serviceMonitor.jobLabel }}
  {{- end }}
  selector:
    matchLabels:
    {{- include "bk-nodeman.selectorLabels" . | nindent 6 }}
    {{- include "bk-nodeman.labels.serviceMonitor" . | nindent 6 }}
  endpoints:
    - port: http
      path: "/metrics"
      {{- if .Values.serviceMonitor.interval }}
      interval: {{ .Values.serviceMonitor.interval }}
      {{- end }}
      {{- if .Values.serviceMonitor.scrapeTimeout }}
      scrapeTimeout: {{ .Values.serviceMonitor.scrapeTimeout }}
      {{- end }}
      {{- if hasKey .Values.serviceMonitor "honorLabels" }}
      honorLabels: {{ .Values.serviceMonitor.honorLabels }}
      {{- end }}
      {{- if .Values.serviceMonitor.relabelings }}
      relabelings: {{- include "common.tplvalues.render" ( dict "value" .Values.serviceMonitor.relabelings "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.serviceMonitor.metricRelabelings }}
      metricRelabelings: {{- include "common.tplvalues.render" ( dict "value" .Values.serviceMonitor.metricRelabelings "context" $) | nindent 8 }}
      {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
{{- end }}
