# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community
Edition) available.
Copyright (C) 2017-2019 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at
http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""


from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='VariableModel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.Char<PERSON><PERSON>(max_length=255, unique=True, verbose_name='\u53d8\u91cf\u7f16\u7801')),
                ('status', models.BooleanField(default=True, verbose_name='\u53d8\u91cf\u662f\u5426\u53ef\u7528')),
            ],
            options={
                'verbose_name': 'Variable\u53d8\u91cf',
                'verbose_name_plural': 'Variable\u53d8\u91cf',
            },
        ),
    ]
