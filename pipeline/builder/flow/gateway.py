# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community
Edition) available.
Copyright (C) 2017-2019 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at
http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""

from pipeline.builder.flow.base import *  # noqa

__all__ = ["ParallelGateway", "ExclusiveGateway", "ConvergeGateway", "ConditionalParallelGateway"]


class ParallelGateway(Element):
    def type(self):
        return PE.ParallelGateway


class ConditionGateway(Element):
    def __init__(self, conditions=None, *args, **kwargs):
        self.conditions = conditions or {}
        super(ConditionGateway, self).__init__(*args, **kwargs)

    def add_condition(self, index, evaluate):
        self.conditions[index] = evaluate

    def link_conditions_with(self, outgoing):
        conditions = {}
        for i, out in enumerate(outgoing):
            conditions[out] = {PE.evaluate: self.conditions[i]}

        return conditions


class ConditionalParallelGateway(ConditionGateway):
    def type(self):
        return PE.ConditionalParallelGateway


class ExclusiveGateway(ConditionGateway):
    def type(self):
        return PE.ExclusiveGateway


class ConvergeGateway(Element):
    def type(self):
        return PE.ConvergeGateway
