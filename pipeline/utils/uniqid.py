# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community
Edition) available.
Copyright (C) 2017-2019 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at
http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""

import uuid

from pipeline.conf import settings


def uniqid():
    return uuid.uuid4().hex


def node_uniqid():
    uid = uniqid()
    return "n%s" % uid[1:] if settings.UUID_DIGIT_STARTS_SENSITIVE else uid


def line_uniqid():
    uid = uniqid()
    return "l%s" % uid[1:] if settings.UUID_DIGIT_STARTS_SENSITIVE else uid
