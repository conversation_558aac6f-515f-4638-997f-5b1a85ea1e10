# Generated by Django 2.2.8 on 2020-06-10 06:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("engine", "0025_multicallbackdata"),
    ]

    operations = [
        migrations.AlterField(
            model_name="pipelineprocess",
            name="is_alive",
            field=models.BooleanField(db_index=True, default=True, verbose_name="该 process 是否还有效"),
        ),
        migrations.AlterField(
            model_name="pipelineprocess",
            name="is_frozen",
            field=models.BooleanField(db_index=True, default=False, verbose_name="该 process 是否被冻结"),
        ),
        migrations.AlterField(
            model_name="pipelineprocess",
            name="is_sleep",
            field=models.BooleanField(db_index=True, default=False, verbose_name="该 process 是否正在休眠"),
        ),
        migrations.AlterField(
            model_name="pipelineprocess",
            name="root_pipeline_id",
            field=models.Char<PERSON>ield(db_index=True, max_length=32, verbose_name="根 pipeline 的 ID"),
        ),
        migrations.AlterField(
            model_name="scheduleservice",
            name="is_scheduling",
            field=models.BooleanField(db_index=True, default=False, verbose_name="是否正在被调度"),
        ),
        migrations.AlterField(
            model_name="noderelationship", name="distance", field=models.IntegerField(db_index=True, verbose_name="距离"),
        ),
    ]
