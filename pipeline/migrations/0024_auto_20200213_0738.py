# -*- coding: utf-8 -*-
# Generated by Django 1.11.23 on 2020-02-13 07:38
from __future__ import unicode_literals

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pipeline', '0023_set_is_revoked'),
    ]

    operations = [
        migrations.AlterField(
            model_name='pipelineinstance',
            name='execution_snapshot',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL,
                                    related_name='execution_snapshot_instances', to='pipeline.Snapshot', verbose_name='用于实例执行的结构数据'),
        ),
        migrations.AlterField(
            model_name='pipelineinstance',
            name='name',
            field=models.CharField(default='default_instance', max_length=128, verbose_name='实例名称'),
        ),
        migrations.AlterField(
            model_name='pipelineinstance',
            name='snapshot',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL,
                                    related_name='snapshot_instances', to='pipeline.Snapshot', verbose_name='实例结构数据，指向实例对应的模板的结构数据'),
        ),
        migrations.AlterField(
            model_name='pipelineinstance',
            name='tree_info',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL,
                                    related_name='tree_info_instances', to='pipeline.TreeInfo', verbose_name='提前计算好的一些流程结构数据'),
        ),
        migrations.AlterField(
            model_name='pipelinetemplate',
            name='name',
            field=models.CharField(default='default_template', max_length=128, verbose_name='模板名称'),
        )
    ]
