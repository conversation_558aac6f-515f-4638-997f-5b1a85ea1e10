# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community
Edition) available.
Copyright (C) 2017-2019 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at
http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""
# Generated by Django 1.11.2 on 2018-01-09 18:25


from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pipeline', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='pipelineinstance',
            name='creator',
            field=models.CharField(blank=True, max_length=32, verbose_name='\u521b\u5efa\u8005'),
        ),
        migrations.AlterField(
            model_name='pipelineinstance',
            name='description',
            field=models.TextField(blank=True, default='', verbose_name='\u63cf\u8ff0'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='pipelineinstance',
            name='executor',
            field=models.CharField(blank=True, max_length=32, verbose_name='\u6267\u884c\u8005'),
        ),
    ]
