# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> is pleased to support the open source community by making 蓝鲸智云PaaS平台社区版 (BlueKing PaaS Community
Edition) available.
Copyright (C) 2017-2019 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at
http://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""
# Generated by Django 1.11.2 on 2017-11-24 10:43


from django.db import migrations, models
import django.db.models.deletion
import pipeline.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PipelineInstance',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('instance_id', models.CharField(max_length=32, unique=True, verbose_name='\u5b9e\u4f8bID')),
                ('name', models.CharField(default='\u9ed8\u8ba4\u5b9e\u4f8b',
                                          max_length=64, verbose_name='\u5b9e\u4f8b\u540d\u79f0')),
                ('creator', models.CharField(max_length=32, verbose_name='\u521b\u5efa\u8005')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='\u521b\u5efa\u65f6\u95f4')),
                ('executor', models.CharField(max_length=32, verbose_name='\u6267\u884c\u8005')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='\u542f\u52a8\u65f6\u95f4')),
                ('finish_time', models.DateTimeField(blank=True, null=True, verbose_name='\u7ed3\u675f\u65f6\u95f4')),
                ('description', models.TextField(blank=True, null=True, verbose_name='\u63cf\u8ff0')),
                ('is_started', models.BooleanField(default=False, verbose_name='\u662f\u5426\u5df2\u7ecf\u542f\u52a8')),
                ('is_finished', models.BooleanField(default=False, verbose_name='\u662f\u5426\u5df2\u7ecf\u5b8c\u6210')),
                ('is_deleted', models.BooleanField(default=False, help_text='\u8868\u793a\u5f53\u524d\u5b9e\u4f8b\u662f\u5426\u5220\u9664',
                                                   verbose_name='\u662f\u5426\u5df2\u7ecf\u5220\u9664')),
            ],
            options={
                'ordering': ['-create_time'],
                'verbose_name': 'Pipeline\u5b9e\u4f8b',
                'verbose_name_plural': 'Pipeline\u5b9e\u4f8b',
            },
        ),
        migrations.CreateModel(
            name='PipelineTemplate',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('template_id', models.CharField(max_length=32, unique=True, verbose_name='\u6a21\u677fID')),
                ('name', models.CharField(default='\u9ed8\u8ba4\u6a21\u677f',
                                          max_length=64, verbose_name='\u6a21\u677f\u540d\u79f0')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='\u521b\u5efa\u65f6\u95f4')),
                ('creator', models.CharField(max_length=32, verbose_name='\u521b\u5efa\u8005')),
                ('description', models.TextField(blank=True, null=True, verbose_name='\u63cf\u8ff0')),
                ('editor', models.CharField(blank=True, max_length=32, null=True, verbose_name='\u4fee\u6539\u8005')),
                ('edit_time', models.DateTimeField(auto_now=True, verbose_name='\u4fee\u6539\u65f6\u95f4')),
                ('is_deleted', models.BooleanField(default=False,
                                                   help_text='\u8868\u793a\u5f53\u524d\u6a21\u677f\u662f\u5426\u5220\u9664', verbose_name='\u662f\u5426\u5220\u9664')),
            ],
            options={
                'ordering': ['-edit_time'],
                'verbose_name': 'Pipeline\u6a21\u677f',
                'verbose_name_plural': 'Pipeline\u6a21\u677f',
            },
        ),
        migrations.CreateModel(
            name='Snapshot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('md5sum', models.CharField(max_length=32, unique=True,
                                            verbose_name='\u5feb\u7167\u5b57\u7b26\u4e32\u7684md5sum')),
                ('create_time', models.DateTimeField(auto_now_add=True, verbose_name='\u521b\u5efa\u65f6\u95f4')),
                ('data', pipeline.models.CompressJSONField(blank=True, null=True)),
            ],
            options={
                'ordering': ['-id'],
                'verbose_name': '\u6a21\u677f\u5feb\u7167',
                'verbose_name_plural': '\u6a21\u677f\u5feb\u7167',
            },
        ),
        migrations.CreateModel(
            name='VariableModel',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=255, unique=True, verbose_name='\u53d8\u91cf\u7f16\u7801')),
                ('status', models.BooleanField(default=True, verbose_name='\u53d8\u91cf\u662f\u5426\u53ef\u7528')),
            ],
            options={
                'ordering': ['-id'],
                'verbose_name': 'lazy \u53d8\u91cf',
                'verbose_name_plural': 'lazy \u53d8\u91cf',
            },
        ),
        migrations.AddField(
            model_name='pipelinetemplate',
            name='snapshot',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                    to='pipeline.Snapshot', verbose_name='\u6a21\u677f\u7ed3\u6784\u6570\u636e'),
        ),
        migrations.AddField(
            model_name='pipelineinstance',
            name='execution_snapshot',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='execution_snapshot',
                                    to='pipeline.Snapshot', verbose_name='\u7528\u4e8e\u5b9e\u4f8b\u6267\u884c\u7684\u7ed3\u6784\u6570\u636e'),
        ),
        migrations.AddField(
            model_name='pipelineinstance',
            name='snapshot',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='snapshot',
                                    to='pipeline.Snapshot', verbose_name='\u5b9e\u4f8b\u7ed3\u6784\u6570\u636e'),
        ),
        migrations.AddField(
            model_name='pipelineinstance',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE,
                                    to='pipeline.PipelineTemplate', verbose_name='Pipeline\u6a21\u677f'),
        ),
    ]
