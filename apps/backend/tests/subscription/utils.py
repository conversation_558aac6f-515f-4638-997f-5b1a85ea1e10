# -*- coding: utf-8 -*-
"""
TencentBlueKing is pleased to support the open source community by making 蓝鲸智云-节点管理(BlueKing-BK-NODEMAN) available.
Copyright (C) 2017-2022 THL A29 Limited, a Tencent company. All rights reserved.
Licensed under the MIT License (the "License"); you may not use this file except in compliance with the License.
You may obtain a copy of the License at https://opensource.org/licenses/MIT
Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
specific language governing permissions and limitations under the License.
"""


class CmdbClient(object):
    @classmethod
    def set_username(cls, *args, **kwargs):
        return

    class cc(object):
        @classmethod
        def list_biz_hosts(cls, *args, **kwargs):
            return {
                "count": 1,
                "info": [
                    {
                        "bk_os_name": "linux centos",
                        "bk_host_id": 1,
                        "bk_os_version": "7.4.1708",
                        "operator": "",
                        "bk_host_name": "VM_1_10_centos",
                        "bk_host_innerip": "127.0.0.1",
                        "bk_os_bit": "64-bit",
                        "bk_cloud_id": 0,
                        "bk_host_outerip": "",
                        "bk_os_type": "1",
                        "bk_bak_operator": "",
                        "bk_cpu_module": "AMD EPYC Processor",
                    }
                ],
            }

        @classmethod
        def list_hosts_without_biz(cls, *args, **kwargs):
            return {
                "count": 1,
                "info": [
                    {
                        "bk_os_name": "linux centos",
                        "bk_host_id": 1,
                        "bk_os_version": "7.4.1708",
                        "operator": "",
                        "bk_host_name": "VM_1_10_centos",
                        "bk_host_innerip": "127.0.0.1",
                        "bk_os_bit": "64-bit",
                        "bk_cloud_id": 0,
                        "bk_host_outerip": "",
                        "bk_os_type": "1",
                        "bk_bak_operator": "",
                        "bk_cpu_module": "AMD EPYC Processor",
                    }
                ],
            }

        @classmethod
        def find_host_biz_relations(cls, *args, **kwargs):
            return [
                {"bk_host_id": 1, "bk_biz_id": 2, "bk_set_id": 1, "bk_module_id": 3},
                {"bk_host_id": 2, "bk_biz_id": 2, "bk_set_id": 1, "bk_module_id": 3},
                {"bk_host_id": 3, "bk_biz_id": 2, "bk_set_id": 1, "bk_module_id": 3},
                {"bk_host_id": 4, "bk_biz_id": 2, "bk_set_id": 1, "bk_module_id": 3},
            ]

        @classmethod
        def search_business(cls, *args, **kwargs):
            return {"info": [{"bk_biz_id": 2, "bk_biz_name": "TEST"}], "count": 1}

        @classmethod
        def search_biz_inst_topo(cls, *args, **kwargs):
            return [
                {
                    "host_count": 0,
                    "default": 0,
                    "bk_obj_name": "business",
                    "bk_obj_id": "biz",
                    "service_instance_count": 0,
                    "child": [
                        {
                            "host_count": 0,
                            "default": 0,
                            "bk_obj_name": "test",
                            "bk_obj_id": "test",
                            "service_instance_count": 0,
                            "child": [
                                {
                                    "host_count": 0,
                                    "default": 0,
                                    "bk_obj_name": "set",
                                    "bk_obj_id": "set",
                                    "service_instance_count": 0,
                                    "child": [
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 29,
                                            "bk_inst_name": "job",
                                        }
                                    ],
                                    "bk_inst_id": 7,
                                    "bk_inst_name": "作业平台",
                                },
                                {
                                    "host_count": 0,
                                    "default": 0,
                                    "bk_obj_name": "set",
                                    "bk_obj_id": "set",
                                    "service_instance_count": 0,
                                    "child": [
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 16,
                                            "bk_inst_name": "beanstalk",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 60,
                                            "bk_inst_name": "ceph-mon",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 61,
                                            "bk_inst_name": "ceph-radosgw",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 21,
                                            "bk_inst_name": "consul",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 14,
                                            "bk_inst_name": "elasticsearch",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 11,
                                            "bk_inst_name": "etcd",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 10,
                                            "bk_inst_name": "influxdb",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 19,
                                            "bk_inst_name": "kafka",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 17,
                                            "bk_inst_name": "mongodb",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 18,
                                            "bk_inst_name": "mysql",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 59,
                                            "bk_inst_name": "nfs",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 15,
                                            "bk_inst_name": "nginx",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 20,
                                            "bk_inst_name": "rabbitmq",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 12,
                                            "bk_inst_name": "redis",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 9,
                                            "bk_inst_name": "redis_cluster",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 13,
                                            "bk_inst_name": "zookeeper",
                                        },
                                    ],
                                    "bk_inst_id": 5,
                                    "bk_inst_name": "公共组件",
                                },
                                {
                                    "host_count": 0,
                                    "default": 0,
                                    "bk_obj_name": "set",
                                    "bk_obj_id": "set",
                                    "service_instance_count": 0,
                                    "child": [
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 5,
                                            "bk_inst_name": "fta",
                                        }
                                    ],
                                    "bk_inst_id": 3,
                                    "bk_inst_name": "故障自愈",
                                },
                                {
                                    "host_count": 0,
                                    "default": 0,
                                    "bk_obj_name": "set",
                                    "bk_obj_id": "set",
                                    "service_instance_count": 0,
                                    "child": [
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 6,
                                            "bk_inst_name": "dataapi",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 7,
                                            "bk_inst_name": "databus",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 58,
                                            "bk_inst_name": "datamanager",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 8,
                                            "bk_inst_name": "monitor",
                                        },
                                    ],
                                    "bk_inst_id": 4,
                                    "bk_inst_name": "数据服务模块",
                                },
                                {
                                    "host_count": 0,
                                    "default": 0,
                                    "bk_obj_name": "set",
                                    "bk_obj_id": "set",
                                    "service_instance_count": 0,
                                    "child": [
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 47,
                                            "bk_inst_name": "gse_alarm",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 42,
                                            "bk_inst_name": "gse_api",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 45,
                                            "bk_inst_name": "gse_btsvr",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 49,
                                            "bk_inst_name": "gse_data",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 56,
                                            "bk_inst_name": "gse_dataop",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 43,
                                            "bk_inst_name": "gse_dba",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 46,
                                            "bk_inst_name": "gse_ops",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 52,
                                            "bk_inst_name": "gse_opts",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 44,
                                            "bk_inst_name": "gse_proc",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 55,
                                            "bk_inst_name": "gse_procmgr",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 57,
                                            "bk_inst_name": "gse_syncdata",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 50,
                                            "bk_inst_name": "gse_task",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 48,
                                            "bk_inst_name": "license",
                                        },
                                    ],
                                    "bk_inst_id": 9,
                                    "bk_inst_name": "管控平台",
                                },
                                {
                                    "host_count": 0,
                                    "default": 0,
                                    "bk_obj_name": "set",
                                    "bk_obj_id": "set",
                                    "service_instance_count": 0,
                                    "child": [
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 33,
                                            "bk_inst_name": "adminserver",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 34,
                                            "bk_inst_name": "apiserver",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 38,
                                            "bk_inst_name": "auditcontroller",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 54,
                                            "bk_inst_name": "cmdb-nginx",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 35,
                                            "bk_inst_name": "datacollection",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 36,
                                            "bk_inst_name": "eventserver",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 39,
                                            "bk_inst_name": "hostcontroller",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 30,
                                            "bk_inst_name": "hostserver",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 31,
                                            "bk_inst_name": "objectcontroller",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 32,
                                            "bk_inst_name": "proccontroller",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 40,
                                            "bk_inst_name": "procserver",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 41,
                                            "bk_inst_name": "toposerver",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 37,
                                            "bk_inst_name": "webserver",
                                        },
                                    ],
                                    "bk_inst_id": 8,
                                    "bk_inst_name": "配置平台",
                                },
                                {
                                    "host_count": 0,
                                    "default": 0,
                                    "bk_obj_name": "set",
                                    "bk_obj_id": "set",
                                    "service_instance_count": 0,
                                    "child": [
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 28,
                                            "bk_inst_name": "appengine",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 23,
                                            "bk_inst_name": "appo",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 24,
                                            "bk_inst_name": "appt",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 22,
                                            "bk_inst_name": "console",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 25,
                                            "bk_inst_name": "esb",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 26,
                                            "bk_inst_name": "login",
                                        },
                                        {
                                            "host_count": 0,
                                            "default": 0,
                                            "bk_obj_name": "module",
                                            "bk_obj_id": "module",
                                            "service_instance_count": 0,
                                            "child": [],
                                            "bk_inst_id": 27,
                                            "bk_inst_name": "paas",
                                        },
                                    ],
                                    "bk_inst_id": 6,
                                    "bk_inst_name": "集成平台",
                                },
                            ],
                            "bk_inst_id": 2,
                            "bk_inst_name": "test1",
                        }
                    ],
                    "bk_inst_id": 2,
                    "bk_inst_name": "蓝鲸",
                }
            ]

        @classmethod
        def get_mainline_object_topo(cls, *args, **kwargs):
            return [
                {"bk_obj_id": "biz"},
                {"bk_obj_id": "set"},
                {"bk_obj_id": "module"},
                {"bk_obj_id": "host"},
            ]

        @classmethod
        def get_biz_internal_module(cls, *args, **kwargs):
            return {"bk_set_id": 10, "module": None, "bk_set_name": "空闲机池"}

        @classmethod
        def search_object_attribute(cls, *args, **kwargs):
            return []

        @classmethod
        def list_service_instance_detail(cls, *args, **kwargs):
            SERVICE_DETAIL = {
                "count": 49,
                "info": [
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 8,
                        "name": "appo",
                        "bk_host_id": 1,
                        "bk_host_innerip": "127.0.0.1",
                        "bk_module_id": 23,
                        "creator": "admin",
                        "modifier": "admin",
                        "create_time": "2019-06-28T16:50:00.422+08:00",
                        "last_time": "2019-06-28T16:50:00.422+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 119,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-server",
                                    "port": "6379",
                                    "last_time": "2019-07-09T13:06:54.32+08:00",
                                    "create_time": "2019-07-09T13:06:54.32+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": ":6379",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 119,
                                    "service_instance_id": 8,
                                    "process_template_id": 26,
                                    "bk_host_id": 2,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 120,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-sentinel",
                                    "port": "16379",
                                    "last_time": "2019-07-09T13:06:54.324+08:00",
                                    "create_time": "2019-07-09T13:06:54.324+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "sentinel",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 120,
                                    "service_instance_id": 8,
                                    "process_template_id": 27,
                                    "bk_host_id": 2,
                                    "bk_supplier_account": "0",
                                },
                            },
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 9,
                        "name": "esb",
                        "bk_host_id": 1,
                        "bk_host_innerip": "127.0.0.1",
                        "bk_module_id": 25,
                        "creator": "admin",
                        "modifier": "admin",
                        "create_time": "2019-06-29T16:32:36.796+08:00",
                        "last_time": "2019-06-29T16:32:36.796+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 121,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-server",
                                    "port": "6379",
                                    "last_time": "2019-07-09T13:06:54.332+08:00",
                                    "create_time": "2019-07-09T13:06:54.332+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": ":6379",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 121,
                                    "service_instance_id": 9,
                                    "process_template_id": 26,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 122,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-sentinel",
                                    "port": "16379",
                                    "last_time": "2019-07-09T13:06:54.336+08:00",
                                    "create_time": "2019-07-09T13:06:54.336+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "sentinel",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 122,
                                    "service_instance_id": 9,
                                    "process_template_id": 27,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            },
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 1,
                        "name": "mysql",
                        "service_template_id": 3,
                        "bk_host_id": 1,
                        "bk_module_id": 18,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.013+08:00",
                        "last_time": "2019-07-09T13:06:54.013+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 110,
                                    "bk_func_name": "mysqld",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "mysqld",
                                    "port": "3306",
                                    "last_time": "2019-07-09T13:06:54.019+08:00",
                                    "create_time": "2019-07-09T13:06:54.019+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 110,
                                    "service_instance_id": 1,
                                    "process_template_id": 11,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 111,
                                    "bk_func_name": "bash",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "mysqld_safe",
                                    "port": "",
                                    "last_time": "2019-07-09T13:06:54.024+08:00",
                                    "create_time": "2019-07-09T13:06:54.024+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "mysqld_safe",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 111,
                                    "service_instance_id": 1,
                                    "process_template_id": 12,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 2,
                        "name": "gse_ops",
                        "service_template_id": 5,
                        "bk_host_id": 1,
                        "bk_module_id": 46,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.075+08:00",
                        "last_time": "2019-07-09T13:06:54.075+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 112,
                                    "bk_func_name": "gse_ops",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "gse_ops",
                                    "port": "58725",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.079+08:00",
                                    "create_time": "2019-07-09T13:06:54.079+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 112,
                                    "service_instance_id": 2,
                                    "process_template_id": 14,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 3,
                        "name": "adminserver",
                        "service_template_id": 7,
                        "bk_host_id": 3,
                        "bk_module_id": 33,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.148+08:00",
                        "last_time": "2019-07-09T13:06:54.148+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 113,
                                    "bk_func_name": "cmdb_adminserver",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_adminserver",
                                    "port": "32004",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.15+08:00",
                                    "create_time": "2019-07-09T13:06:54.15+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 113,
                                    "service_instance_id": 3,
                                    "process_template_id": 20,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 4,
                        "name": "toposerver",
                        "service_template_id": 8,
                        "bk_host_id": 3,
                        "bk_module_id": 41,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.179+08:00",
                        "last_time": "2019-07-09T13:06:54.179+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 114,
                                    "bk_func_name": "cmdb_toposerver",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_toposerver",
                                    "port": "32002",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.182+08:00",
                                    "create_time": "2019-07-09T13:06:54.182+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 114,
                                    "service_instance_id": 4,
                                    "process_template_id": 21,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 5,
                        "name": "gse_task",
                        "service_template_id": 9,
                        "bk_host_id": 1,
                        "bk_module_id": 50,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.207+08:00",
                        "last_time": "2019-07-09T13:06:54.207+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 115,
                                    "bk_func_name": "gse_task",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "gse_task",
                                    "port": ",48668,48671,48329",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.209+08:00",
                                    "create_time": "2019-07-09T13:06:54.209+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 115,
                                    "service_instance_id": 5,
                                    "process_template_id": 22,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 6,
                        "name": "gse_syncdata",
                        "service_template_id": 10,
                        "bk_host_id": 1,
                        "bk_module_id": 57,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.234+08:00",
                        "last_time": "2019-07-09T13:06:54.234+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 116,
                                    "bk_func_name": "gse_syncdata",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "gse_syncdata",
                                    "port": "52050",
                                    "last_time": "2019-07-09T13:06:54.236+08:00",
                                    "create_time": "2019-07-09T13:06:54.236+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 116,
                                    "service_instance_id": 6,
                                    "process_template_id": 23,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 7,
                        "name": "redis_cluster",
                        "service_template_id": 12,
                        "bk_host_id": 1,
                        "bk_module_id": 9,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.297+08:00",
                        "last_time": "2019-07-09T13:06:54.297+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 117,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-server",
                                    "port": "6379",
                                    "last_time": "2019-07-09T13:06:54.308+08:00",
                                    "create_time": "2019-07-09T13:06:54.308+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": ":6379",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 117,
                                    "service_instance_id": 7,
                                    "process_template_id": 26,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 118,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-sentinel",
                                    "port": "16379",
                                    "last_time": "2019-07-09T13:06:54.312+08:00",
                                    "create_time": "2019-07-09T13:06:54.312+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "sentinel",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 118,
                                    "service_instance_id": 7,
                                    "process_template_id": 27,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 8,
                        "name": "redis_cluster",
                        "service_template_id": 12,
                        "bk_host_id": 2,
                        "bk_module_id": 9,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.317+08:00",
                        "last_time": "2019-07-09T13:06:54.317+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 119,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-server",
                                    "port": "6379",
                                    "last_time": "2019-07-09T13:06:54.32+08:00",
                                    "create_time": "2019-07-09T13:06:54.32+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": ":6379",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 119,
                                    "service_instance_id": 8,
                                    "process_template_id": 26,
                                    "bk_host_id": 2,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 120,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-sentinel",
                                    "port": "16379",
                                    "last_time": "2019-07-09T13:06:54.324+08:00",
                                    "create_time": "2019-07-09T13:06:54.324+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "sentinel",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 120,
                                    "service_instance_id": 8,
                                    "process_template_id": 27,
                                    "bk_host_id": 2,
                                    "bk_supplier_account": "0",
                                },
                            },
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 9,
                        "name": "redis_cluster",
                        "service_template_id": 12,
                        "bk_host_id": 3,
                        "bk_module_id": 9,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.329+08:00",
                        "last_time": "2019-07-09T13:06:54.329+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 121,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-server",
                                    "port": "6379",
                                    "last_time": "2019-07-09T13:06:54.332+08:00",
                                    "create_time": "2019-07-09T13:06:54.332+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": ":6379",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 121,
                                    "service_instance_id": 9,
                                    "process_template_id": 26,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 122,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-sentinel",
                                    "port": "16379",
                                    "last_time": "2019-07-09T13:06:54.336+08:00",
                                    "create_time": "2019-07-09T13:06:54.336+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "sentinel",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 122,
                                    "service_instance_id": 9,
                                    "process_template_id": 27,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            },
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 10,
                        "name": "redis",
                        "service_template_id": 14,
                        "bk_host_id": 1,
                        "bk_module_id": 12,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.384+08:00",
                        "last_time": "2019-07-09T13:06:54.384+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 123,
                                    "bk_func_name": "redis-server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "redis-server",
                                    "port": "6379",
                                    "last_time": "2019-07-09T13:06:54.387+08:00",
                                    "create_time": "2019-07-09T13:06:54.387+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": ":6379",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 123,
                                    "service_instance_id": 10,
                                    "process_template_id": 29,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 11,
                        "name": "zookeeper",
                        "service_template_id": 15,
                        "bk_host_id": 1,
                        "bk_module_id": 13,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.414+08:00",
                        "last_time": "2019-07-09T13:06:54.414+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 124,
                                    "bk_func_name": "java",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "zk-java",
                                    "port": "2181,3888",
                                    "last_time": "2019-07-09T13:06:54.417+08:00",
                                    "create_time": "2019-07-09T13:06:54.417+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "org.apache.zookeeper.server.quorum.QuorumPeerMain",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 124,
                                    "service_instance_id": 11,
                                    "process_template_id": 30,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 12,
                        "name": "zookeeper",
                        "service_template_id": 15,
                        "bk_host_id": 2,
                        "bk_module_id": 13,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.421+08:00",
                        "last_time": "2019-07-09T13:06:54.421+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 125,
                                    "bk_func_name": "java",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "zk-java",
                                    "port": "2181,3888",
                                    "last_time": "2019-07-09T13:06:54.424+08:00",
                                    "create_time": "2019-07-09T13:06:54.424+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "org.apache.zookeeper.server.quorum.QuorumPeerMain",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 125,
                                    "service_instance_id": 12,
                                    "process_template_id": 30,
                                    "bk_host_id": 2,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 13,
                        "name": "zookeeper",
                        "service_template_id": 15,
                        "bk_host_id": 3,
                        "bk_module_id": 13,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.428+08:00",
                        "last_time": "2019-07-09T13:06:54.428+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 126,
                                    "bk_func_name": "java",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "zk-java",
                                    "port": "2181,3888",
                                    "last_time": "2019-07-09T13:06:54.433+08:00",
                                    "create_time": "2019-07-09T13:06:54.433+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "org.apache.zookeeper.server.quorum.QuorumPeerMain",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 126,
                                    "service_instance_id": 13,
                                    "process_template_id": 30,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 14,
                        "name": "gse_opts",
                        "service_template_id": 16,
                        "bk_host_id": 1,
                        "bk_module_id": 52,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.459+08:00",
                        "last_time": "2019-07-09T13:06:54.459+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 127,
                                    "bk_func_name": "gse_opts",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "gse_opts",
                                    "port": "58636",
                                    "last_time": "2019-07-09T13:06:54.462+08:00",
                                    "create_time": "2019-07-09T13:06:54.462+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 127,
                                    "service_instance_id": 14,
                                    "process_template_id": 31,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 15,
                        "name": "rabbitmq",
                        "service_template_id": 19,
                        "bk_host_id": 3,
                        "bk_module_id": 20,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.574+08:00",
                        "last_time": "2019-07-09T13:06:54.574+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 128,
                                    "bk_func_name": "beam.smp",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "rabbitmq-beam.smp",
                                    "port": "5672,15672,25672",
                                    "last_time": "2019-07-09T13:06:54.576+08:00",
                                    "create_time": "2019-07-09T13:06:54.576+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "rabbitmq",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 128,
                                    "service_instance_id": 15,
                                    "process_template_id": 44,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 16,
                        "name": "eventserver",
                        "service_template_id": 21,
                        "bk_host_id": 3,
                        "bk_module_id": 36,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.636+08:00",
                        "last_time": "2019-07-09T13:06:54.636+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 129,
                                    "bk_func_name": "cmdb_eventserver",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_eventserver",
                                    "port": "32005",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.64+08:00",
                                    "create_time": "2019-07-09T13:06:54.64+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 129,
                                    "service_instance_id": 16,
                                    "process_template_id": 46,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 17,
                        "name": "gse_btsvr",
                        "service_template_id": 22,
                        "bk_host_id": 1,
                        "bk_module_id": 45,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.672+08:00",
                        "last_time": "2019-07-09T13:06:54.672+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 130,
                                    "bk_func_name": "gse_btsvr",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "gse_btsvr",
                                    "port": "10020,58930,58925",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.675+08:00",
                                    "create_time": "2019-07-09T13:06:54.675+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 130,
                                    "service_instance_id": 17,
                                    "process_template_id": 47,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 18,
                        "name": "gse_procmgr",
                        "service_template_id": 24,
                        "bk_host_id": 1,
                        "bk_module_id": 55,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.727+08:00",
                        "last_time": "2019-07-09T13:06:54.727+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 131,
                                    "bk_func_name": "gse_procmgr",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "gse_procmgr",
                                    "port": "52030",
                                    "last_time": "2019-07-09T13:06:54.73+08:00",
                                    "create_time": "2019-07-09T13:06:54.73+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 131,
                                    "service_instance_id": 18,
                                    "process_template_id": 49,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 19,
                        "name": "appo",
                        "service_template_id": 25,
                        "bk_host_id": 1,
                        "bk_module_id": 23,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.771+08:00",
                        "last_time": "2019-07-09T13:06:54.771+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 132,
                                    "bk_func_name": "paas_agent",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "paas_agent",
                                    "port": "4245",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.774+08:00",
                                    "create_time": "2019-07-09T13:06:54.774+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 132,
                                    "service_instance_id": 19,
                                    "process_template_id": 50,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 133,
                                    "bk_func_name": "docker-containerd",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "docker-containerd",
                                    "port": "",
                                    "last_time": "2019-07-09T13:06:54.778+08:00",
                                    "create_time": "2019-07-09T13:06:54.778+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 133,
                                    "service_instance_id": 19,
                                    "process_template_id": 51,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 134,
                                    "bk_func_name": "dockerd",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "dockerd",
                                    "port": "",
                                    "last_time": "2019-07-09T13:06:54.781+08:00",
                                    "create_time": "2019-07-09T13:06:54.781+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 134,
                                    "service_instance_id": 19,
                                    "process_template_id": 52,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 135,
                                    "bk_func_name": "nginx",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "nginx-paasagent",
                                    "port": "8010",
                                    "last_time": "2019-07-09T13:06:54.785+08:00",
                                    "create_time": "2019-07-09T13:06:54.785+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 135,
                                    "service_instance_id": 19,
                                    "process_template_id": 53,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 20,
                        "name": "objectcontroller",
                        "service_template_id": 26,
                        "bk_host_id": 3,
                        "bk_module_id": 31,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.808+08:00",
                        "last_time": "2019-07-09T13:06:54.808+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 136,
                                    "bk_func_name": "cmdb_objectcontroller",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_objectcontroller",
                                    "port": "31001",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.811+08:00",
                                    "create_time": "2019-07-09T13:06:54.811+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 136,
                                    "service_instance_id": 20,
                                    "process_template_id": 54,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 21,
                        "name": "apiserver",
                        "service_template_id": 27,
                        "bk_host_id": 3,
                        "bk_module_id": 34,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.833+08:00",
                        "last_time": "2019-07-09T13:06:54.833+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 137,
                                    "bk_func_name": "cmdb_apiserver",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_apiserver",
                                    "port": "33031",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.836+08:00",
                                    "create_time": "2019-07-09T13:06:54.836+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 137,
                                    "service_instance_id": 21,
                                    "process_template_id": 55,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 22,
                        "name": "webserver",
                        "service_template_id": 28,
                        "bk_host_id": 3,
                        "bk_module_id": 37,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.868+08:00",
                        "last_time": "2019-07-09T13:06:54.868+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 138,
                                    "bk_func_name": "cmdb_webserver",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_webserver",
                                    "port": "33083",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.871+08:00",
                                    "create_time": "2019-07-09T13:06:54.871+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 138,
                                    "service_instance_id": 22,
                                    "process_template_id": 56,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 23,
                        "name": "hostcontroller",
                        "service_template_id": 29,
                        "bk_host_id": 3,
                        "bk_module_id": 39,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.9+08:00",
                        "last_time": "2019-07-09T13:06:54.9+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 139,
                                    "bk_func_name": "cmdb_hostcontroller",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_hostcontroller",
                                    "port": "31002",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.903+08:00",
                                    "create_time": "2019-07-09T13:06:54.903+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 139,
                                    "service_instance_id": 23,
                                    "process_template_id": 57,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 24,
                        "name": "gse_alarm",
                        "service_template_id": 30,
                        "bk_host_id": 1,
                        "bk_module_id": 47,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.932+08:00",
                        "last_time": "2019-07-09T13:06:54.932+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 140,
                                    "bk_func_name": "gse_alarm",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "gse_alarm",
                                    "port": "",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.943+08:00",
                                    "create_time": "2019-07-09T13:06:54.943+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 140,
                                    "service_instance_id": 24,
                                    "process_template_id": 58,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 25,
                        "name": "procserver",
                        "service_template_id": 31,
                        "bk_host_id": 3,
                        "bk_module_id": 40,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.968+08:00",
                        "last_time": "2019-07-09T13:06:54.968+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 141,
                                    "bk_func_name": "cmdb_procserver",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_procserver",
                                    "port": "32003",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.97+08:00",
                                    "create_time": "2019-07-09T13:06:54.97+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 141,
                                    "service_instance_id": 25,
                                    "process_template_id": 59,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 26,
                        "name": "gse_api",
                        "service_template_id": 32,
                        "bk_host_id": 1,
                        "bk_module_id": 42,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:54.995+08:00",
                        "last_time": "2019-07-09T13:06:54.995+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 142,
                                    "bk_func_name": "gse_api",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "gse_api",
                                    "port": "59313,50002",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:54.998+08:00",
                                    "create_time": "2019-07-09T13:06:54.998+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 142,
                                    "service_instance_id": 26,
                                    "process_template_id": 60,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 27,
                        "name": "license",
                        "service_template_id": 33,
                        "bk_host_id": 1,
                        "bk_module_id": 48,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.027+08:00",
                        "last_time": "2019-07-09T13:06:55.027+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 143,
                                    "bk_func_name": "license_server",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "license_server",
                                    "port": "8443",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:55.03+08:00",
                                    "create_time": "2019-07-09T13:06:55.03+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 143,
                                    "service_instance_id": 27,
                                    "process_template_id": 61,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 28,
                        "name": "nginx",
                        "service_template_id": 35,
                        "bk_host_id": 1,
                        "bk_module_id": 15,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.082+08:00",
                        "last_time": "2019-07-09T13:06:55.082+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 144,
                                    "bk_func_name": "nginx",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "nginx-paas-http",
                                    "port": "80",
                                    "last_time": "2019-07-09T13:06:55.085+08:00",
                                    "create_time": "2019-07-09T13:06:55.085+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 144,
                                    "service_instance_id": 28,
                                    "process_template_id": 63,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 145,
                                    "bk_func_name": "nginx",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "nginx-paas-https",
                                    "port": "443",
                                    "last_time": "2019-07-09T13:06:55.089+08:00",
                                    "create_time": "2019-07-09T13:06:55.089+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 145,
                                    "service_instance_id": 28,
                                    "process_template_id": 64,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            },
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 29,
                        "name": "datacollection",
                        "service_template_id": 36,
                        "bk_host_id": 3,
                        "bk_module_id": 35,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.117+08:00",
                        "last_time": "2019-07-09T13:06:55.117+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 146,
                                    "bk_func_name": "cmdb_datacollection",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_datacollection",
                                    "port": "33084",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:55.121+08:00",
                                    "create_time": "2019-07-09T13:06:55.121+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 146,
                                    "service_instance_id": 29,
                                    "process_template_id": 65,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 30,
                        "name": "gse_dataop",
                        "service_template_id": 37,
                        "bk_host_id": 1,
                        "bk_module_id": 56,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.148+08:00",
                        "last_time": "2019-07-09T13:06:55.148+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 147,
                                    "bk_func_name": "gse_dataop",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "gse_dataop",
                                    "port": "",
                                    "last_time": "2019-07-09T13:06:55.152+08:00",
                                    "create_time": "2019-07-09T13:06:55.152+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 147,
                                    "service_instance_id": 30,
                                    "process_template_id": 66,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 31,
                        "name": "consul",
                        "service_template_id": 40,
                        "bk_host_id": 1,
                        "bk_module_id": 21,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.238+08:00",
                        "last_time": "2019-07-09T13:06:55.238+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 148,
                                    "bk_func_name": "consul",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "consul-agent",
                                    "port": "8301,8500,53",
                                    "last_time": "2019-07-09T13:06:55.241+08:00",
                                    "create_time": "2019-07-09T13:06:55.241+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 148,
                                    "service_instance_id": 31,
                                    "process_template_id": 73,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 32,
                        "name": "consul",
                        "service_template_id": 40,
                        "bk_host_id": 2,
                        "bk_module_id": 21,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.246+08:00",
                        "last_time": "2019-07-09T13:06:55.246+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 149,
                                    "bk_func_name": "consul",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "consul-agent",
                                    "port": "8301,8500,53",
                                    "last_time": "2019-07-09T13:06:55.249+08:00",
                                    "create_time": "2019-07-09T13:06:55.249+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 149,
                                    "service_instance_id": 32,
                                    "process_template_id": 73,
                                    "bk_host_id": 2,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 33,
                        "name": "consul",
                        "service_template_id": 40,
                        "bk_host_id": 3,
                        "bk_module_id": 21,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.254+08:00",
                        "last_time": "2019-07-09T13:06:55.254+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 150,
                                    "bk_func_name": "consul",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "bk_process_name": "consul-agent",
                                    "port": "8301,8500,53",
                                    "last_time": "2019-07-09T13:06:55.257+08:00",
                                    "create_time": "2019-07-09T13:06:55.257+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 150,
                                    "service_instance_id": 33,
                                    "process_template_id": 73,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 34,
                        "name": "esb",
                        "service_template_id": 41,
                        "bk_host_id": 1,
                        "bk_module_id": 25,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.281+08:00",
                        "last_time": "2019-07-09T13:06:55.281+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 151,
                                    "bk_func_name": "uwsgi",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "paas-esb",
                                    "port": "8002",
                                    "last_time": "2019-07-09T13:06:55.284+08:00",
                                    "create_time": "2019-07-09T13:06:55.284+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "paas-esb",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 151,
                                    "service_instance_id": 34,
                                    "process_template_id": 74,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 35,
                        "name": "hostserver",
                        "service_template_id": 42,
                        "bk_host_id": 3,
                        "bk_module_id": 30,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.313+08:00",
                        "last_time": "2019-07-09T13:06:55.313+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 152,
                                    "bk_func_name": "cmdb_hostserver",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_hostserver",
                                    "port": "32001",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:55.316+08:00",
                                    "create_time": "2019-07-09T13:06:55.316+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 152,
                                    "service_instance_id": 35,
                                    "process_template_id": 75,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 36,
                        "name": "proccontroller",
                        "service_template_id": 50,
                        "bk_host_id": 3,
                        "bk_module_id": 32,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.572+08:00",
                        "last_time": "2019-07-09T13:06:55.572+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 153,
                                    "bk_func_name": "cmdb_proccontroller",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_proccontroller",
                                    "port": "31003",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:55.575+08:00",
                                    "create_time": "2019-07-09T13:06:55.575+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 153,
                                    "service_instance_id": 36,
                                    "process_template_id": 95,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 37,
                        "name": "gse_dba",
                        "service_template_id": 51,
                        "bk_host_id": 1,
                        "bk_module_id": 43,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.604+08:00",
                        "last_time": "2019-07-09T13:06:55.604+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 154,
                                    "bk_func_name": "gse_dba",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "gse_dba",
                                    "port": "58859",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:55.607+08:00",
                                    "create_time": "2019-07-09T13:06:55.607+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 154,
                                    "service_instance_id": 37,
                                    "process_template_id": 96,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 38,
                        "name": "gse_data",
                        "service_template_id": 52,
                        "bk_host_id": 1,
                        "bk_module_id": 49,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.635+08:00",
                        "last_time": "2019-07-09T13:06:55.635+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 155,
                                    "bk_func_name": "gse_data",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "gse_data",
                                    "port": "58625",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:55.639+08:00",
                                    "create_time": "2019-07-09T13:06:55.639+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 155,
                                    "service_instance_id": 38,
                                    "process_template_id": 97,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 39,
                        "name": "auditcontroller",
                        "service_template_id": 53,
                        "bk_host_id": 3,
                        "bk_module_id": 38,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.667+08:00",
                        "last_time": "2019-07-09T13:06:55.667+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 156,
                                    "bk_func_name": "cmdb_auditcontroller",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "cmdb_auditcontroller",
                                    "port": "31004",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-09T13:06:55.67+08:00",
                                    "create_time": "2019-07-09T13:06:55.67+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 156,
                                    "service_instance_id": 39,
                                    "process_template_id": 98,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 40,
                        "name": "mongodb",
                        "service_template_id": 55,
                        "bk_host_id": 1,
                        "bk_module_id": 17,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.725+08:00",
                        "last_time": "2019-07-09T13:06:55.725+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 157,
                                    "bk_func_name": "mongod",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "mongod",
                                    "port": "27017",
                                    "last_time": "2019-07-09T13:06:55.728+08:00",
                                    "create_time": "2019-07-09T13:06:55.728+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 157,
                                    "service_instance_id": 40,
                                    "process_template_id": 100,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 41,
                        "name": "mongodb",
                        "service_template_id": 55,
                        "bk_host_id": 2,
                        "bk_module_id": 17,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.733+08:00",
                        "last_time": "2019-07-09T13:06:55.733+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 158,
                                    "bk_func_name": "mongod",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "mongod",
                                    "port": "27017",
                                    "last_time": "2019-07-09T13:06:55.736+08:00",
                                    "create_time": "2019-07-09T13:06:55.736+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 158,
                                    "service_instance_id": 41,
                                    "process_template_id": 100,
                                    "bk_host_id": 2,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 42,
                        "name": "mongodb",
                        "service_template_id": 55,
                        "bk_host_id": 3,
                        "bk_module_id": 17,
                        "creator": "cc_system",
                        "modifier": "cc_system",
                        "create_time": "2019-07-09T13:06:55.741+08:00",
                        "last_time": "2019-07-09T13:06:55.741+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 159,
                                    "bk_func_name": "mongod",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "bk_process_name": "mongod",
                                    "port": "27017",
                                    "last_time": "2019-07-09T13:06:55.745+08:00",
                                    "create_time": "2019-07-09T13:06:55.745+08:00",
                                    "bk_biz_id": 2,
                                    "protocol": "1",
                                    "bk_supplier_account": "0",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 159,
                                    "service_instance_id": 42,
                                    "process_template_id": 100,
                                    "bk_host_id": 3,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 106,
                        "name": "127.0.0.1_es-java_9300,10004",
                        "service_template_id": 2,
                        "bk_host_id": 1,
                        "bk_host_innerip": "127.0.0.1",
                        "bk_module_id": 14,
                        "creator": "admin",
                        "modifier": "admin",
                        "create_time": "2019-07-18T10:58:44.679+08:00",
                        "last_time": "2019-07-18T10:58:44.679+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "proc_num": 0,
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 168,
                                    "bk_func_name": "java",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "priority": 0,
                                    "reload_cmd": "",
                                    "bk_process_name": "es-java",
                                    "port": "9300,10004",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "auto_time_gap": 0,
                                    "last_time": "2019-07-18T10:58:44.785+08:00",
                                    "create_time": "2019-07-18T10:58:44.785+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "timeout": 0,
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "org.elasticsearch.bootstrap.Elasticsearch",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 168,
                                    "service_instance_id": 106,
                                    "process_template_id": 10,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 107,
                        "name": "127.0.0.1_asdf_",
                        "bk_host_id": 1,
                        "bk_host_innerip": "127.0.0.1",
                        "bk_module_id": 29,
                        "creator": "admin",
                        "modifier": "admin",
                        "create_time": "2019-07-18T11:00:14.189+08:00",
                        "last_time": "2019-07-18T11:00:14.189+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": None},
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 169,
                                    "bk_func_name": "asdf",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "reload_cmd": "",
                                    "bk_process_name": "asdf",
                                    "port": "",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "last_time": "2019-07-18T11:00:14.265+08:00",
                                    "create_time": "2019-07-18T11:00:14.265+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 169,
                                    "service_instance_id": 107,
                                    "process_template_id": 0,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 108,
                        "name": "127.0.0.1_asdf_",
                        "bk_host_id": 1,
                        "bk_host_innerip": "127.0.0.1",
                        "bk_module_id": 29,
                        "creator": "admin",
                        "modifier": "admin",
                        "create_time": "2019-07-18T11:00:33.404+08:00",
                        "last_time": "2019-07-18T11:00:33.404+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": None},
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 170,
                                    "bk_func_name": "asdf",
                                    "work_path": "",
                                    "bind_ip": "",
                                    "reload_cmd": "",
                                    "bk_process_name": "asdf",
                                    "port": "",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "last_time": "2019-07-18T11:00:33.459+08:00",
                                    "create_time": "2019-07-18T11:00:33.459+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 170,
                                    "service_instance_id": 108,
                                    "process_template_id": 0,
                                    "bk_host_id": 1,
                                    "bk_supplier_account": "",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 109,
                        "name": "*********_consul-agent_8301,8500,53",
                        "service_template_id": 40,
                        "bk_host_id": 1022,
                        "bk_host_innerip": "*********",
                        "bk_module_id": 21,
                        "creator": "admin",
                        "modifier": "admin",
                        "create_time": "2019-08-02T12:35:31.223+08:00",
                        "last_time": "2019-08-02T12:35:31.223+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 171,
                                    "bk_func_name": "consul",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "reload_cmd": "",
                                    "bk_process_name": "consul-agent",
                                    "port": "8301,8500,53",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "last_time": "2019-08-02T12:35:31.25+08:00",
                                    "create_time": "2019-08-02T12:35:31.25+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 171,
                                    "service_instance_id": 109,
                                    "process_template_id": 73,
                                    "bk_host_id": 1022,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                    {
                        "metadata": {"label": {"bk_biz_id": "2"}},
                        "id": 110,
                        "name": "127.0.0.1_consul-agent_8301,8500,53",
                        "service_template_id": 40,
                        "bk_host_id": 1023,
                        "bk_host_innerip": "127.0.0.1",
                        "bk_module_id": 21,
                        "creator": "admin",
                        "modifier": "admin",
                        "create_time": "2019-08-02T12:35:35.26+08:00",
                        "last_time": "2019-08-02T12:35:35.26+08:00",
                        "bk_supplier_account": "0",
                        "service_category_id": 10,
                        "process_instances": [
                            {
                                "process": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "stop_cmd": "",
                                    "restart_cmd": "",
                                    "face_stop_cmd": "",
                                    "bk_process_id": 172,
                                    "bk_func_name": "consul",
                                    "work_path": "/data/bkee",
                                    "bind_ip": "",
                                    "reload_cmd": "",
                                    "bk_process_name": "consul-agent",
                                    "port": "8301,8500,53",
                                    "pid_file": "",
                                    "auto_start": False,
                                    "last_time": "2019-08-02T12:35:35.305+08:00",
                                    "create_time": "2019-08-02T12:35:35.305+08:00",
                                    "bk_biz_id": 2,
                                    "start_cmd": "",
                                    "bk_func_id": "",
                                    "user": "",
                                    "protocol": "1",
                                    "description": "",
                                    "bk_supplier_account": "0",
                                    "bk_start_param_regex": "",
                                },
                                "relation": {
                                    "metadata": {"label": {"bk_biz_id": "2"}},
                                    "bk_process_id": 172,
                                    "service_instance_id": 110,
                                    "process_template_id": 73,
                                    "bk_host_id": 1023,
                                    "bk_supplier_account": "0",
                                },
                            }
                        ],
                    },
                ],
            }
            SERVICE_DETAIL["info"] = [x for x in SERVICE_DETAIL["info"] if x["id"] == 10]
            return SERVICE_DETAIL

        @classmethod
        def find_host_by_topo(cls, *args, **kwargs):
            return {"count": 4, "info": LIST_BIZ_HOSTS_WITHOUT_INFO}


DEFAULT_AP_ID = 1

FIND_HOST_BIZ_RELATIONS = [
    {"bk_host_id": 1, "bk_biz_id": 2, "bk_set_id": 1, "bk_module_id": 3},
    {"bk_host_id": 2, "bk_biz_id": 2, "bk_set_id": 1, "bk_module_id": 3},
    {"bk_host_id": 3, "bk_biz_id": 2, "bk_set_id": 1, "bk_module_id": 3},
    {"bk_host_id": 4, "bk_biz_id": 2, "bk_set_id": 1, "bk_module_id": 3},
]


def list_biz_hosts_without_info_client(*args, **kwargs):
    return LIST_BIZ_HOSTS_WITHOUT_INFO


LIST_BIZ_HOSTS_WITHOUT_INFO = [
    {
        "bk_os_name": "linux centos",
        "bk_host_id": 1,
        "operator": "",
        "bk_host_name": "VM_1_10_centos",
        "bk_host_innerip": "127.0.0.1",
        "bk_os_bit": "64-bit",
        "bk_cloud_id": 0,
        "bk_host_outerip": "",
        "bk_os_type": "1",
        "bk_bak_operator": "",
        "bk_cpu_module": "AMD EPYC Processor",
    }
]
LIST_BIZ_HOSTS = FIND_HOST_BY_TOPO = {"count": 4, "info": LIST_BIZ_HOSTS_WITHOUT_INFO}
