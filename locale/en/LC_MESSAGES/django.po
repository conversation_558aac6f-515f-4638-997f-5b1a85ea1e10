# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-07-20 18:49+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: apps/backend/agent/artifact_builder/base.py:83
#: apps/backend/plugin/handler.py:72 apps/backend/views.py:224
#, python-brace-format
msgid "文件不存在：file_path -> {file_path}"
msgstr "File does not exist: file_path -> {file_path}"

#: apps/backend/agent/artifact_builder/base.py:204
#, python-brace-format
msgid "gsectl 文件不存在：file_path -> {file_path}"
msgstr "File gsectl does not exist: file_path -> {file_path}"

#: apps/backend/agent/artifact_builder/base.py:301
#, python-brace-format
msgid ""
"Agent 包保存错误，期望保存到 -> {package_target_path}, 实际保存到 -> "
"{storage_path}"
msgstr ""
"Error saving Agent package, expect to save to -> {package_target_path}, "
"actually save to -> {storage_path}"

#: apps/backend/agent/artifact_builder/base.py:341
msgid "版本文件不存在"
msgstr "Version file does not exist"

#: apps/backend/agent/artifact_builder/base.py:361
msgid "版本日志文件不存在"
msgstr "Version log file does not exist"

#: apps/backend/agent/artifact_builder/proxy.py:44
#, python-brace-format
msgid "Proxy 包解压后不存在 {base_pkg_dir} 目录"
msgstr ""
"The {base_pkg_dir} directory does not exist after Proxy package decompression"

#: apps/backend/agent/artifact_builder/proxy.py:55
#, python-brace-format
msgid "构建 Proxy 所需 Agent 包不存在：file_path -> {file_path}"
msgstr ""
"Agent package required to build Proxy rs not exists: file_path -> {file_path}"

#: apps/backend/agent/artifact_builder/proxy.py:78
msgid "构建 Proxy 所需 Agent 不存在 bin 路径"
msgstr "The Agent required to build Proxy does not exist bin path"

#: apps/backend/agent/artifact_builder/proxy.py:85
#, python-brace-format
msgid ""
"构建 Proxy 所需二进制 [{svr_exe}] 不存在：svr_exe_path -> {svr_exe_path}"
msgstr ""
"The binary [{svr_exe}] required to build proxy does not exist: svr_exe_path -"
"> {svr_exe_path}"

#: apps/backend/agent/manager.py:97
msgid "卸载Agent"
msgstr "Uninstall Agent"

#: apps/backend/agent/manager.py:106
msgid "卸载Proxy"
msgstr "Uninstall Proxy"

#: apps/backend/agent/manager.py:179
msgid "启动 NGINX 服务"
msgstr "Start NGINX service"

#: apps/backend/agent/solution_maker.py:153
#: apps/backend/subscription/steps/agent.py:485
#: apps/backend/subscription/steps/agent.py:504 apps/node_man/constants.py:296
msgid "卸载"
msgstr "Uninstall"

#: apps/backend/agent/solution_maker.py:153
#: apps/backend/components/collections/agent_new/components.py:65
#: apps/backend/subscription/steps/agent.py:236
#: apps/backend/subscription/steps/agent.py:363
#: apps/backend/subscription/steps/agent.py:552
#: apps/backend/subscription/steps/agent.py:596 apps/node_man/constants.py:289
msgid "安装"
msgstr "Install"

#: apps/backend/agent/solution_maker.py:361
msgid "创建依赖目录"
msgstr "Create dependencies"

#: apps/backend/agent/solution_maker.py:376
#, python-brace-format
msgid "创建 {dir}"
msgstr "Create {dir}"

#: apps/backend/agent/solution_maker.py:426
#, python-brace-format
msgid "下载 {filename}"
msgstr "Download {filename}"

#: apps/backend/agent/solution_maker.py:438
#, python-brace-format
msgid "为 {filename} 添加执行权限"
msgstr "Add execution permissions for {filename}"

#: apps/backend/agent/solution_maker.py:448
#, python-brace-format
msgid "执行 {filename}"
msgstr "Execute {filename}"

#: apps/backend/agent/solution_maker.py:539
#: apps/backend/agent/solution_maker.py:674
#, python-brace-format
msgid "通过 {solution_type_alias} 进行{setup_type_alias}"
msgstr "Execute {setup_type_alias} by {solution_type_alias}"

#: apps/backend/agent/solution_maker.py:546
#, python-brace-format
msgid ""
"{solution_description}（若目标机器已安装 Cygwin，推荐使用该方案，否则请使用"
"【{batch}】方案）"
msgstr ""
"{solution_description} (If the target machine has Cygwin installed, this "
"solution is recommended, otherwise, please use the【{batch}】solution)"

#: apps/backend/agent/solution_maker.py:560
msgid "依赖文件下载"
msgstr "Dependency file download"

#: apps/backend/agent/solution_maker.py:583
#, python-brace-format
msgid "下载{setup_type_alias}脚本并赋予执行权限"
msgstr "Download {setup_type_alias} script and add execution permissions"

#: apps/backend/agent/solution_maker.py:590
#: apps/backend/agent/solution_maker.py:659
#: apps/backend/agent/solution_maker.py:758
#: apps/backend/agent/solution_maker.py:764
#, python-brace-format
msgid "下载{setup_type_alias}脚本"
msgstr "Download {setup_type_alias} script"

#: apps/backend/agent/solution_maker.py:598
#, python-brace-format
msgid "为 {script_file_name} 添加执行权限"
msgstr "Add execution permissions for {script_file_name}"

#: apps/backend/agent/solution_maker.py:608
#: apps/backend/agent/solution_maker.py:614
#: apps/backend/agent/solution_maker.py:665
#: apps/backend/agent/solution_maker.py:805
#: apps/backend/agent/solution_maker.py:811
#, python-brace-format
msgid "执行{setup_type_alias}脚本"
msgstr "Execute {setup_type_alias} script"

#: apps/backend/agent/solution_maker.py:632
#, python-brace-format
msgid "下载依赖文件到 {dest_dir} 下"
msgstr "Download the dependency file to {dest_dir}"

#: apps/backend/agent/solution_maker.py:654
#, python-brace-format
msgid "执行{setup_type_alias}命令"
msgstr "Execute {setup_type_alias} command"

#: apps/backend/agent/solution_maker.py:822
#, python-brace-format
msgid "通过 {solution_type_alias} 在代理上进行{setup_type_alias}"
msgstr "Perform {solution_type_alias} on proxy {setup_type_alias}"

#: apps/backend/api/constants.py:117 apps/node_man/constants.py:385
#: apps/node_man/constants.py:724 apps/node_man/constants.py:760
#: apps/node_man/tools/job.py:55
msgid "执行成功"
msgstr "Succeed"

#: apps/backend/api/constants.py:118 apps/node_man/constants.py:384
#: apps/node_man/constants.py:723 apps/node_man/constants.py:759
msgid "正在执行"
msgstr "Running"

#: apps/backend/api/constants.py:119
msgid "Agent 状态异常"
msgstr "Agent state error"

#: apps/backend/api/constants.py:120
msgid "进程正在运行中，无需启动"
msgstr "Process is running, no need to start"

#: apps/backend/api/constants.py:121
msgid "进程当前未运行，无需停止"
msgstr "The process is not currently running and does not need to be stopped"

#: apps/backend/api/constants.py:122
msgid "操作类型非法"
msgstr "Illegal operation type"

#: apps/backend/api/constants.py:123
msgid "进程启动失败（后置检查状态为未运行）"
msgstr "Process start failed (post-check status is not running)"

#: apps/backend/api/constants.py:124 apps/node_man/constants.py:386
#: apps/node_man/constants.py:721 apps/node_man/constants.py:757
#: apps/node_man/constants.py:761
msgid "执行失败"
msgstr "Failed"

#: apps/backend/api/errors.py:22
msgid "无响应"
msgstr "Not responding"

#: apps/backend/api/errors.py:23
#, python-brace-format
msgid "API({api_name})没有返回响应"
msgstr "API ({api_name}) returned no response"

#: apps/backend/api/errors.py:30
msgid "接口调用错误"
msgstr "API call error"

#: apps/backend/api/errors.py:31
#, python-brace-format
msgid "API({api_name})调用出错：{error_message}"
msgstr "Error calling API ({api_name}): {error_message}"

#: apps/backend/api/errors.py:38
msgid "Job轮询超时"
msgstr "Job poll timeout"

#: apps/backend/api/errors.py:39
#, python-brace-format
msgid "Job任务({job_instance_id})轮询超时"
msgstr "Job task ({job_instance_id}) polling timeout"

#: apps/backend/api/errors.py:46
msgid "GSE轮询超时"
msgstr "GSE poll timeout"

#: apps/backend/api/errors.py:47
#, python-brace-format
msgid "GSE任务({task_id})轮询超时"
msgstr "GSE task ({task_id}) poll timed out"

#: apps/backend/components/collections/agent_new/add_or_update_hosts.py:101
#: apps/backend/components/collections/agent_new/add_or_update_hosts.py:137
#, python-brace-format
msgid ""
"主机期望注册到业务【ID：{except_bk_biz_id}】，但实际存在于业务【ID: "
"{actual_biz_id}】，请前往该业务进行安装"
msgstr ""
"The host expects to register to business [ID: {except_bk_biz_id}], but "
"actually exists in the business ID: {actual_biz_id}, please go to the "
"business to install"

#: apps/backend/components/collections/agent_new/add_or_update_hosts.py:289
#, python-brace-format
msgid ""
"更新 CMDB 主机信息:\n"
" {params}"
msgstr ""
"Update CMDB host information: \n"
" {params}"

#: apps/backend/components/collections/agent_new/add_or_update_hosts.py:326
#, python-brace-format
msgid ""
"添加主机到业务 {bk_biz_name}[{bk_biz_id}]:\n"
" {params}"
msgstr ""
"Add host to business {bk_biz_name}[{bk_biz_id}]:\n"
" {params}"

#: apps/backend/components/collections/agent_new/base.py:198
#, python-brace-format
msgid "所选安装通道「{name}」 没有可用跳板机"
msgstr "The selected installation channel '{name} has no jumper available"

#: apps/backend/components/collections/agent_new/base.py:210
#, python-brace-format
msgid "管控区域 -> {bk_cloud_id} 下无存活的 Proxy"
msgstr "No live proxy under \"BK-Net\" -> {bk_cloud_id}"

#: apps/backend/components/collections/agent_new/bind_host_agent.py:64
#, python-brace-format
msgid ""
"已将 Agent[bk_agent_id: {bk_agent_id}] 绑定到 主机[bk_host_id: {bk_host_id}]"
msgstr ""
"Agent [bk_agent_id: {bk_agent_id}] has bind to the host [bk_host_id: "
"{bk_host_id}]"

#: apps/backend/components/collections/agent_new/bind_host_agent.py:108
msgid "bk_agent_id 不存在"
msgstr "bk_agent_id does not exist"

#: apps/backend/components/collections/agent_new/check_agent_status.py:42
msgid "Agent 状态【正常】"
msgstr "Agent status [normal]"

#: apps/backend/components/collections/agent_new/check_agent_status.py:44
msgid "Agent 状态【异常】"
msgstr "Agent status [abnormal]"

#: apps/backend/components/collections/agent_new/check_policy_gse_to_proxy.py:64
#: apps/backend/tests/components/collections/agent_new/test_check_policy_gse_to_proxy.py:82
msgid "请检查出口IP是否正确或策略是否开通"
msgstr "Please check whether the egress IP is correct or the policy is enabled"

#: apps/backend/components/collections/agent_new/check_policy_gse_to_proxy.py:91
#, python-brace-format
msgid ""
"请确保 GSE File Server 公网IP（{file_endpoints}）到 Proxy 出口IP"
"（{proxy_outer_ip}）的 {port_polices} 访问策略正常。\n"
"另外请注意：\n"
"1. 可执行 curl ipinfo.io 确认「出口IP」\n"
"2. 不支持多台 Proxy 使用同一个「出口IP」\n"
"3. 请保证「出口IP」固定不变"
msgstr ""
"Please make sure the GSE File Server public IP ({file_endpoints}) to the "
"{port_polices} access Proxy public IP({proxy_outer_ip}) policy OK.\n"
"Also note:\n"
"1. Execute curl ipinfo.io to confirm the 'export IP'\n"
"2. Multiple proxies using the same 'exit IP' are not supported\n"
"3. Please ensure that the 'export IP' is fixed"

#: apps/backend/components/collections/agent_new/choose_access_point.py:73
msgid "选择接入点失败"
msgstr "Failed to select access point"

#: apps/backend/components/collections/agent_new/choose_access_point.py:75
#, python-brace-format
msgid "已选择 [{ap_name}] 作为本次安装的接入点"
msgstr ""
"[{ap_name}] has been selected as the access point for this installation"

#: apps/backend/components/collections/agent_new/choose_access_point.py:156
#, python-brace-format
msgid "连接至接入点 [{ap_name}] 的平均延迟为 {ping_time}ms"
msgstr ""
"The average latency to connect to access point [{ap_name}] is {ping_time}ms"

#: apps/backend/components/collections/agent_new/choose_access_point.py:163
#, python-brace-format
msgid ""
"主机 <--> 接入点网络情况检测结果：\n"
"{detect_logs_str}"
msgstr ""
"Host <--> Access point network condition test result:\n"
"{detect_logs_str}"

#: apps/backend/components/collections/agent_new/choose_access_point.py:171
msgid "自动选择接入点失败，接入点均 ping 不可达"
msgstr ""
"Failed to automatically select access points, all access points are ping "
"unreachable"

#: apps/backend/components/collections/agent_new/choose_access_point.py:358
#, python-brace-format
msgid "管控区域 -> {bk_cloud_id} 下无 GSE版本 -> {gse_version} 存活的 Proxy"
msgstr "No live proxy({gse_version}) under \"BK-Net\" -> {bk_cloud_id}"

#: apps/backend/components/collections/agent_new/choose_access_point.py:410
#, python-brace-format
msgid ""
"主机所在「管控区域」 「{bk_cloud_name}」[{bk_cloud_id}] 下无 GSE 版本 -> "
"{gse_version} 的接入点，请联系管理员添加映射关系"
msgstr ""
"There is no access point of GSE version -> {gse_version} under the \"BK-"
"Net\" {bk_cloud_name} [{bk_cloud_id}] where the Host is located, please "
"contact the administrator to add the mapping relationship"

#: apps/backend/components/collections/agent_new/choose_access_point.py:429
#, python-brace-format
msgid ""
"已选择主机所在「管控区域」「{bk_cloud_name}」[{bk_cloud_id}] 指定的接入点 "
"[{ap_name}]"
msgstr ""
"The access point specified by the \"BK-Net\" {bk_cloud_name} [{bk_cloud_id}] "
"where the Host is located [{ap_name}]"

#: apps/backend/components/collections/agent_new/choose_access_point.py:523
msgid "自动选择接入点失败，请到全局配置新建对应版本接入点"
msgstr ""
"Failed to automatically select access point, please go to global "
"configuration to new a new access point"

#: apps/backend/components/collections/agent_new/choose_access_point.py:557
#, python-brace-format
msgid "当前主机已分配接入点 [{ap_name}]"
msgstr "The current host has assigned access point [{ap_name}]"

#: apps/backend/components/collections/agent_new/components.py:47
msgid "查询主机密码"
msgstr "Query host password"

#: apps/backend/components/collections/agent_new/components.py:53
msgid "选择接入点"
msgstr "Select access point"

#: apps/backend/components/collections/agent_new/components.py:59
msgid "注册主机到配置平台"
msgstr "Register host to configuration platform"

#: apps/backend/components/collections/agent_new/components.py:71
msgid "安装预设插件"
msgstr "Install preset plugin"

#: apps/backend/components/collections/agent_new/components.py:77
msgid "配置策略"
msgstr "Configure policy"

#: apps/backend/components/collections/agent_new/components.py:83
#: apps/backend/plugin/manager.py:61
msgid "查询Agent状态"
msgstr "Query Agent Status"

#: apps/backend/components/collections/agent_new/components.py:89
msgid "重载Agent配置"
msgstr "Reload Agent Configuration"

#: apps/backend/components/collections/agent_new/components.py:95
#: apps/backend/subscription/steps/agent.py:325
#: apps/backend/subscription/steps/agent.py:344 apps/node_man/constants.py:290
#: apps/node_man/constants.py:472
msgid "重启"
msgstr "Restart"

#: apps/backend/components/collections/agent_new/components.py:101
msgid "更新主机进程状态"
msgstr "Update host process status"

#: apps/backend/components/collections/agent_new/components.py:107
msgid "检查Agent状态"
msgstr "Check Agent Status"

#: apps/backend/components/collections/agent_new/components.py:113
msgid "下发升级包"
msgstr "Transfer upgrade package"

#: apps/backend/components/collections/agent_new/components.py:119
msgid "检测 GSE Server 到 Proxy 策略"
msgstr "Detecting GSE Server to Proxy Policy"

#: apps/backend/components/collections/agent_new/components.py:125
msgid "下发升级脚本命令"
msgstr "Send upgrade script command"

#: apps/backend/components/collections/agent_new/components.py:131
msgid "渲染并下发Agent配置"
msgstr "Render and deliver Agent configuration"

#: apps/backend/components/collections/agent_new/components.py:137
#: apps/backend/components/collections/agent_new/wait.py:22
msgid "等待"
msgstr "Wait"

#: apps/backend/components/collections/agent_new/components.py:143
msgid "执行脚本"
msgstr "Execute script"

#: apps/backend/components/collections/agent_new/components.py:149
msgid "分发文件"
msgstr "Distribution file"

#: apps/backend/components/collections/agent_new/components.py:155
msgid "分发配置"
msgstr "Distribution configuration"

#: apps/backend/components/collections/agent_new/components.py:161
msgid "下发文件到Proxy"
msgstr "Transfer file to Proxy"

#: apps/backend/components/collections/agent_new/components.py:167
msgid "更新安装信息"
msgstr "Update installation info"

#: apps/backend/components/collections/agent_new/components.py:173
msgid "绑定主机 Agent 信息"
msgstr "Bind host Agent information"

#: apps/backend/components/collections/agent_new/components.py:179
msgid "主机解绑 Agent 信息"
msgstr "Unbind host Agent information"

#: apps/backend/components/collections/agent_new/components.py:185
msgid "新增或更新主机信息"
msgstr "Add or Update host information"

#: apps/backend/components/collections/agent_new/components.py:191
msgid "升级为 Agent-ID 配置"
msgstr "Upgrade to Agent-ID configured"

#: apps/backend/components/collections/agent_new/components.py:197
msgid "推送主机身份信息"
msgstr "Push host identify information"

#: apps/backend/components/collections/agent_new/components.py:203
msgid "下发 Agent 安装包到 Proxy"
msgstr "Send Agent installation package to Proxy"

#: apps/backend/components/collections/agent_new/components.py:209
msgid "检查Agent功能"
msgstr "Check Agent ability"

#: apps/backend/components/collections/agent_new/components.py:215
msgid "推送环境变量文件"
msgstr "Push environment file"

#: apps/backend/components/collections/agent_new/components.py:221
msgid "安装额外Agent"
msgstr "Install additional agent"

#: apps/backend/components/collections/agent_new/configure_policy.py:54
msgid "到Gse和Nginx的策略配置成功"
msgstr "Policies to Gse and Nginx are configured successfully"

#: apps/backend/components/collections/agent_new/configure_policy.py:59
msgid "配置到Gse和Nginx的策略失败请联系节点管理维护人员"
msgstr ""
"Failed to configure policies to Gse and Nginx, please contact node "
"management and maintainers"

#: apps/backend/components/collections/agent_new/get_agent_status.py:43
#, python-brace-format
msgid "期望的 Agent 状态为 {expect_status_alias}「{expect_status}」"
msgstr "The expected agent status is {expect_status_alias}[{expect_status}]"

#: apps/backend/components/collections/agent_new/get_agent_status.py:106
#, python-brace-format
msgid ""
"查询 GSE 得到主机 [{host_key}] Agent 状态 -> {status_alias}「{status}」, 版"
"本 -> {version}"
msgstr ""
"Query GSE to get host [{host_key}] Agent status -> {status_alias}[{status}], "
"version -> {version}"

#: apps/backend/components/collections/agent_new/get_agent_status.py:110
msgid "无"
msgstr "None"

#: apps/backend/components/collections/agent_new/get_agent_status.py:146
msgid "查询 GSE 超时"
msgstr "Query GSE timed out"

#: apps/backend/components/collections/agent_new/install.py:205
msgid "等待手动执行安装命令"
msgstr "Waiting for manual install command"

#: apps/backend/components/collections/agent_new/install.py:316
#: apps/backend/utils/ssh.py:416 apps/exceptions.py:115
msgid "认证信息已过期, 请重装并填入认证信息"
msgstr ""
"The authentication information has expired, please reload and fill in the "
"authentication information"

#: apps/backend/components/collections/agent_new/install.py:322
#: apps/backend/components/collections/agent_new/install.py:332
#: apps/backend/components/collections/agent_new/install.py:533
#, python-brace-format
msgid "执行命令: {cmd}"
msgstr "Execute command: {cmd}"

#: apps/backend/components/collections/agent_new/install.py:338
msgid ""
"正在安装 Windows AGENT, 命令执行失败，请确认: \n"
"1. 检查文件共享相关服务，确认以下服务均已开启\n"
"    - Function Discovery Resource Publication\n"
"    - SSDP Discovery \n"
"    - UPnP Device Host\n"
"    - Server\n"
"    - NetLogon // 如果没有加入域，可以不启动这个\n"
"    - TCP/IP NetBIOS Helper\n"
"2. 开启网卡 Net BOIS \n"
"3. 开启文件共享 Net share \n"
"4. 检查防火墙是否有放开 139/135/445 端口 \n"
msgstr ""
"Installing Windows AGENT, command failed, please confirm:\n"
"1. Check file sharing related services and make sure the following services "
"are enabled\n"
" - Function Discovery Resource Publication\n"
" - SSDP Discovery \n"
" - UPnP Device Host\n"
"-Server\n"
" - NetLogon // If not joined to a domain, this can be disabled\n"
" - TCP/IP NetBIOS Helper\n"
"2. Enable the network card Net BOIS \n"
"3. Enable file sharing Net share \n"
"4. Check if the firewall has opened ports 139/135/445\n"

#: apps/backend/components/collections/agent_new/install.py:353
#: apps/backend/components/collections/agent_new/install.py:389
msgid "远程登录失败，等待自动重试，重试失败将打印异常信息。"
msgstr ""
"Remote sign in failed, waiting for automatic retry, if retry fails, an "
"exception message will be printed."

#: apps/backend/components/collections/agent_new/install.py:356
#: apps/backend/components/collections/agent_new/install.py:392
msgid "远程登录失败"
msgstr "Remote sign in failed"

#: apps/backend/components/collections/agent_new/install.py:378
#, python-brace-format
msgid "推送文件 {localpath} 到目标机器路径 {dest_dir}"
msgstr "Push file {localpath} to the target machine path {dest_dir}"

#: apps/backend/components/collections/agent_new/install.py:404
#, python-brace-format
msgid "主机的上游节点为: {upstream_nodes}"
msgstr "The upstream nodes of the host are: {upstream_nodes}"

#: apps/backend/components/collections/agent_new/install.py:408
#, python-brace-format
msgid "已选择 {inner_ip} 作为本次安装的跳板机"
msgstr "{inner_ip} has been selected as the springboard for this installation"

#: apps/backend/components/collections/agent_new/install.py:446
#, python-brace-format
msgid ""
"作业任务ID为[{job_instance_id}]，点击跳转到<a href=\"{link}\" "
"target=\"_blank\">[作业平台]</a>"
msgstr ""
"The JOB task ID is [{job_instance_id}], click to jump to <a href=\"{link}\" "
"target=\"_blank\">[Blueking JOB]</a>"

#: apps/backend/components/collections/agent_new/install.py:460
#, python-brace-format
msgid ""
"作业执行中，如果卡在这里较长时间，请检查：\n"
"1. P-Agent({host_inner_ip}) 到 Proxy({jump_server_ip}) 的 {download_port}、"
"{proxy_pass_port} 是否可连通。 \n"
"2. Proxy是否已正确完成所有安装步骤且状态正常。 \n"
"3. 点击上面链接跳转到作业平台查看任务执行情况。\n"
msgstr ""
"Job execution, if stuck here for a long time, please check:\n"
"1. P-Agent({host_inner_ip}) to {download_port} of Proxy({jump_server_ip}),"
"{proxy_pass_port} is reachable.\n"
"2. Whether the Proxy has completed all installation steps correctly and is "
"in a normal state.\n"
"3. Click the link above to jump to the job platform to view the task "
"execution status.\n"

#: apps/backend/components/collections/agent_new/install.py:524
#, python-brace-format
msgid ""
"推送下列文件到「{dest_dir}」\n"
"{filenames_str}"
msgstr "Push the following files to {dest_dir}\"n {filenames_str}"

#: apps/backend/components/collections/agent_new/install.py:693
msgid "安装超时"
msgstr "Install timed out"

#: apps/backend/components/collections/agent_new/install_other_agent.py:77
#, python-brace-format
msgid "当前主机所处业务[{bk_biz_id}] 无需要额外安装 GSE [{gse_version}] Agent."
msgstr ""
"The current host's biz [{bk_biz_id}] does not require additional "
"installation of GSE [{gse_version}] Agent."

#: apps/backend/components/collections/agent_new/install_other_agent.py:86
#, python-brace-format
msgid "未配置与接入点ID:[{ap_id}]对应的映射,跳过此安装步骤"
msgstr ""
"The mapping corresponding to access point ID: [{ap_id}] is not configured, "
"skipping this installation step."

#: apps/backend/components/collections/agent_new/install_other_agent.py:135
#, python-brace-format
msgid ""
"{log}\n"
"作业任务ID为 [{node_man_task_id}], 点击跳转到 <a href=\"{link}\" "
"target=\"_blank\">[节点管理]</a>"
msgstr ""
"{log}\n"
"The NODEMAN task ID is [{node_man_task_id}], click to jump to <a "
"href=\"{link}\" target=\"_blank\">[Blueking NODEMAN]</a>"

#: apps/backend/components/collections/agent_new/install_other_agent.py:137
#: apps/backend/components/collections/agent_new/install_other_agent.py:148
msgid "安装额外Agent任务已启动"
msgstr "Install additional agent task started"

#: apps/backend/components/collections/agent_new/install_other_agent.py:145
#, python-brace-format
msgid ""
"{log}\n"
" <span style=\"color: #ff9c01\">等待手动安装, 作业任务ID为 "
"[{node_man_task_id}], 请点击跳转到 <a href=\"{link}\" target=\"_blank\">[节点"
"管理]</a>根据提示完成操作.</span>"
msgstr ""
"{log}\n"
"Waiting for manual installation. The NODEMAN task ID is [{node_man_task_id}],"
"click to jump to <a href=\"{link}\" target=\"_blank\">[Blueking NODEMAN]</a> "
"follow the prompts to complete the operation."

#: apps/backend/components/collections/agent_new/install_other_agent.py:201
#: apps/backend/components/collections/agent_new/install_other_agent.py:222
#, python-brace-format
msgid ""
"{log}\n"
"作业任务ID为 [{node_man_task_id}]，点击跳转到 <a href=\"{link}\" "
"target=\"_blank\">[节点管理]</a> 查看详情"
msgstr ""
"{log}\n"
"The NODEMAN task ID is [{node_man_task_id}], click to jump to <a "
"href=\"{link}\" target=\"_blank\">[Blueking NODEMAN]</a>"

#: apps/backend/components/collections/agent_new/install_other_agent.py:203
msgid "安装额外Agent失败"
msgstr "Failed to install additional agent"

#: apps/backend/components/collections/agent_new/install_other_agent.py:215
msgid "安装额外Agent失败, 请点任务链接查看详情"
msgstr ""
"Failed to install additional agent, Please click on the task link to view "
"details"

#: apps/backend/components/collections/agent_new/install_other_agent.py:223
msgid "安装额外Agent超时"
msgstr "Install additional agent timeout"

#: apps/backend/components/collections/agent_new/push_host_identifier.py:50
msgid "推送主机身份的任务为: [{}]"
msgstr "The task of pushing the host identify is: [{}]"

#: apps/backend/components/collections/agent_new/push_host_identifier.py:97
msgid "推送主机身份失败"
msgstr "Push the host identity failed"

#: apps/backend/components/collections/agent_new/push_host_identifier.py:108
msgid "推送主机身份超时"
msgstr "Push the host identity timeout"

#: apps/backend/components/collections/agent_new/query_password.py:59
msgid ""
"若 OA TICKET 过期，请重新登录 OA 后再重试您的操作。请注意不要直接使用此任务中"
"的重试功能~"
msgstr ""
"If OA TICKET expires, please sign in to OA again and try your operation "
"again. Please be careful not to use the retry function in this task directly "
"~"

#: apps/backend/components/collections/agent_new/query_password.py:140
msgid ""
"登录认证信息已被清空\n"
"- 若为重试操作，请新建任务重新发起"
msgstr ""
"Sign in authentication information has been cleared \n"
"- If it is a retry operation, please new a new task and re-initiate"

#: apps/backend/components/collections/agent_new/query_password.py:169
msgid "当前主机验证类型无需查询密码"
msgstr "Current host verify without having to type a password query"

#: apps/backend/components/collections/agent_new/register_host.py:166
#, python-brace-format
msgid ""
"注册主机参数为:\n"
" {params}"
msgstr ""
"The registered host parameter is:\n"
"{params}"

#: apps/backend/components/collections/agent_new/register_host.py:175
#, python-brace-format
msgid ""
"注册主机失败：\n"
"{error_msgs}"
msgstr ""
"Failed to register host:\n"
"{error_msgs}"

#: apps/backend/components/collections/agent_new/register_host.py:238
msgid "查询CMDB主机失败，未在CMDB中查询到主机信息"
msgstr "Failed to query CMDB host, no host information was queried in CMDB"

#: apps/backend/components/collections/agent_new/register_host.py:254
#, python-brace-format
msgid ""
"主机期望注册到「{bk_biz_id}」[{bk_biz_name}]，但实际存在于业务「ID: "
"{actual_biz_id}」，请前往该业务进行安装"
msgstr ""
"The host expects to register with {bk_biz_id} [{bk_biz_name}], but actually "
"exists in the business ID:  {actual_biz_id}, please go to the business to "
"install"

#: apps/backend/components/collections/agent_new/register_host.py:405
msgid "注册CMDB完成"
msgstr "Registration to CMDB completed"

#: apps/backend/components/collections/agent_new/unbind_host_agent.py:50
#, python-brace-format
msgid ""
"已将 Agent[bk_agent_id: {bk_agent_id}] 和 主机[bk_host_id: {bk_host_id}] 之间"
"的绑定关系解除"
msgstr ""
"Between agent [bk_agent_id: {bk_agent_id}] and host [bk_host_id: "
"{bk_host_id}] binding relationship hase been release"

#: apps/backend/components/collections/agent_new/unbind_host_agent.py:72
msgid "不存在绑定关系，无需解绑"
msgstr "No binding relationship, not need to unbind"

#: apps/backend/components/collections/agent_new/update_process_status.py:49
#, python-brace-format
msgid "更新主机状态为{status}"
msgstr "Update host status to {status}"

#: apps/backend/components/collections/agent_new/upgrade_to_agent_id.py:42
msgid "升级 Agent-ID 配置失败"
msgstr "Upgrade Agent-ID configuration failed"

#: apps/backend/components/collections/base.py:89
#, python-brace-format
msgid ""
"{act_name} 失败: {exc}，请先尝试查看错误日志进行处理，若无法解决，请联系管理"
"员处理"
msgstr ""
"{act_name} failed: {exc}, please try to check the error log to deal with it, "
"if it can't be solved, please contact the management manager to deal with"

#: apps/backend/components/collections/base.py:369
#, python-brace-format
msgid ""
"{act_name} 已终止，可整体重试/重试。（details: {revoke_sub_inst_id_set}）"
msgstr ""
"{act_name} terminated, retry/retry as a whole. (details: "
"{revoke_sub_inst_id_set})"

#: apps/backend/components/collections/base.py:392
#, python-brace-format
msgid "{act_name} 失败，请先尝试查看日志并处理，若无法解决，请联系管理员处理。"
msgstr ""
"{act_name} failed, please try to view the log and deal with it. If it cannot "
"be solved, please contact the administrator to deal with it."

#: apps/backend/components/collections/base.py:403
#, python-brace-format
msgid "{act_name} 成功"
msgstr "{act_name} succeeded"

#: apps/backend/components/collections/base.py:425
#, python-brace-format
msgid "开始 {act_name}."
msgstr "Start {act_name}."

#: apps/backend/components/collections/common/remote.py:37
msgid "SSH通道可用"
msgstr "SSH tunnel available"

#: apps/backend/components/collections/common/remote.py:38
msgid "SSH通道可用但登录信息有误"
msgstr "SSH channel available but wrong sign in information"

#: apps/backend/components/collections/common/remote.py:39
msgid "SSH通道不可用"
msgstr "SSH channel unavailable"

#: apps/backend/components/collections/common/remote.py:137
#, python-brace-format
msgid "当前登录用户为「{account}」，请确保该用户具有 sudo 权限"
msgstr ""
"The current sign-in user is {account}, please make sure this user has sudo "
"privileges"

#: apps/backend/components/collections/core.py:37
msgid "使用 ssh 会话执行命令"
msgstr "Execute command using ssh session"

#: apps/backend/components/collections/core.py:38
msgid "使用 wmiexe 会话执行命令"
msgstr "Execute command using wmiexe session"

#: apps/backend/components/collections/core.py:39
msgid "使用 job 执行命令"
msgstr "Execute command using job"

#: apps/backend/components/collections/core.py:40
msgid "查询密码"
msgstr "Query password"

#: apps/backend/components/collections/core.py:41
msgid "主机写入操作"
msgstr "Host write actions"

#: apps/backend/components/collections/job.py:151
#, python-brace-format
msgid ""
"下发配置文件 [{file_names}] 到目标机器路径 [{file_target_path}]，若下发失败，"
"请检查作业平台所部署的机器是否已安装AGENT"
msgstr ""
"Push config file [{file_names}] to the target machine path "
"[{file_target_path}], if the delivery fails,Please check whether the machine "
"deployed by the job platform has AGENT installed"

#: apps/backend/components/collections/job.py:155
#, python-brace-format
msgid "快速执行脚本 {script_name}"
msgstr "Fast execute scripts {script_name}"

#: apps/backend/components/collections/job.py:157
msgid "调用作业平台"
msgstr "Call Blueking Job"

#: apps/backend/components/collections/job.py:168
#, python-brace-format
msgid ""
"{log}\n"
"作业任务ID为 [{job_instance_id}]，点击跳转到 <a href=\"{link}\" "
"target=\"_blank\">[作业平台]</a>"
msgstr ""
"{log}\n"
"The JOB task ID is [{job_instance_id}], click to jump to <a href=\"{link}\" "
"target=\"_blank\">[Blueking JOB]</a>"

#: apps/backend/components/collections/job.py:250
#, python-brace-format
msgid ""
"作业执行状态 -> {status}「{ip_status_msg}」, 主机任务状态码 -> {error_code}"
"「{err_msg}」"
msgstr ""
"Job execution status -> {status} '{ip_status_msg}', host task status code -> "
"{error_code} '{err_msg}'"

#: apps/backend/components/collections/job.py:260
#, python-brace-format
msgid "作业平台执行失败: {err_msg}"
msgstr "Job task failed: {err_msg}"

#: apps/backend/components/collections/job.py:312
msgid "该步骤无需等待作业平台执行结果"
msgstr ""
"This step does not need to wait for the execution result of the job platform"

#: apps/backend/components/collections/job.py:369
msgid "作业平台执行任务超时"
msgstr "Job task execution task timed out"

#: apps/backend/components/collections/plugin.py:167
msgid "插件部署失败，重试次数 +1"
msgstr "Plugin deployment failed, retries +1"

#: apps/backend/components/collections/plugin.py:375
msgid "主机不存在或未同步"
msgstr "Host does not exist or is not in sync"

#: apps/backend/components/collections/plugin.py:514
#, python-brace-format
msgid "{bk_host_id}:AGENT异常[{status}]，请尝试恢复AGENT状态或重装AGENT。"
msgstr ""
"{bk_host_id}: AGENT is abnormal [{status}], please try to restore AGENT "
"status or reinstall AGENT."

#: apps/backend/components/collections/plugin.py:866
msgid "主机[{}]在ip->[{}]上无可用端口"
msgstr "Host [{}] has no available port on ip->[{}]"

#: apps/backend/components/collections/plugin.py:1012
#, python-brace-format
msgid ""
"无需渲染配置，直接进入下一步骤，subscription_instance_ids -> "
"{subscription_instance_ids}"
msgstr ""
"No rendering configuration, go directly to the next step, "
"subscription_instance_ids -> {subscription_instance_ids}"

#: apps/backend/components/collections/plugin.py:1244
msgid "GSE任务轮询超时"
msgstr "GSE task polling timeout"

#: apps/backend/components/collections/plugin.py:1282
#, python-brace-format
msgid "GSE任务查无结果, {task_id}, {key}"
msgstr "GSE task search no result, {task_id}, {key}"

#: apps/backend/components/collections/plugin.py:1432
#: apps/node_man/models.py:1900
msgid "策略"
msgstr "Policy"

#: apps/backend/components/collections/plugin.py:1432
#: apps/backend/components/collections/plugin.py:1481
#: apps/node_man/models.py:565
msgid "订阅"
msgstr "Subscribe"

#: apps/backend/components/collections/plugin.py:1437
#, python-brace-format
msgid "{sub_alias} -> {id}「{name}」 已启用，删除失败"
msgstr "{sub_alias} -> {id} {name} enabled, delete failed"

#: apps/backend/components/collections/plugin.py:1447
#, python-brace-format
msgid "{sub_alias} -> {id}「{name}」，已部署节点数 -> {proc_count}"
msgstr "{sub_alias} -> {id}{name}, number of deployed nodes -> {proc_count}"

#: apps/backend/components/collections/plugin.py:1456
#, python-brace-format
msgid "{sub_alias} -> {id}「{name}」 已部署节点数不为 0，跳过"
msgstr "{sub_alias} -> {id}{name} The number of deployed nodes is not 0, skip"

#: apps/backend/components/collections/plugin.py:1465
#, python-brace-format
msgid "{sub_alias} -> {id}「{name}」删除成功"
msgstr "{sub_alias} -> {id} {name} deleted successfully"

#: apps/backend/components/collections/plugin.py:1485
#, python-brace-format
msgid "{sub_alias} -> {id} 仅提供单次执行能力，保持{enable_alias}状态"
msgstr ""
"{sub_alias} -> {id} only provides single execution capability, keep "
"{enable_alias} state"

#: apps/backend/components/collections/plugin.py:1488
#: apps/backend/components/collections/plugin.py:1503
#: apps/backend/constants.py:137
msgid "启用"
msgstr "Enable"

#: apps/backend/components/collections/plugin.py:1488
#: apps/backend/components/collections/plugin.py:1503
msgid "停用"
msgstr "Deactivate"

#: apps/backend/components/collections/plugin.py:1499
#, python-brace-format
msgid "{sub_alias} -> {id}「{name}」切换为：{enable_alias}"
msgstr "{sub_alias} -> {id} {name} switch to: {enable_alias}"

#: apps/backend/components/collections/subsubscription.py:91
#, python-brace-format
msgid "{node_name}-{sub_step_node_name} 失败"
msgstr "{node_name}-{sub_step_node_name} failed"

#: apps/backend/components/collections/subsubscription.py:107
#, python-brace-format
msgid "{node_name} 执行超时"
msgstr "{node_name} executed timed out"

#: apps/backend/components/collections/subsubscription.py:112
#, python-brace-format
msgid "{node_name} 执行成功"
msgstr "{node_name} executed successfully"

#: apps/backend/components/collections/subsubscription.py:165
#, python-brace-format
msgid "子订阅【ID：{sub_id}】任务创建失败：{err_msg}"
msgstr "Sub subscription [ID: {sub_id}] task creation failed: {err_msg}"

#: apps/backend/components/collections/subsubscription.py:191
#, python-brace-format
msgid "子订阅【ID：{no_ready_sub_ids}】任务创建超时"
msgstr "Sub subscription [ID: {no_ready_sub_ids}] task creation timeout"

#: apps/backend/components/collections/subsubscription.py:212
msgid "部分子订阅任务创建失败"
msgstr "Some sub-subscription tasks creation failed"

#: apps/backend/constants.py:100 apps/backend/constants.py:121
msgid "新安装"
msgstr "New install"

#: apps/backend/constants.py:101
msgid "版本变更"
msgstr "Version change"

#: apps/backend/constants.py:102
msgid "配置变更"
msgstr "Config change"

#: apps/backend/constants.py:103
msgid "进程数量不匹配"
msgstr "Number of processes does not match"

#: apps/backend/constants.py:104
msgid "进程状态异常"
msgstr "Process state abnormal"

#: apps/backend/constants.py:105
msgid "无需变更"
msgstr "No changes required"

#: apps/backend/constants.py:106
msgid "从范围中移除"
msgstr "Remove from scope"

#: apps/backend/constants.py:107
msgid "未同步的主机"
msgstr "Unsynced host"

#: apps/backend/constants.py:108
msgid "手动操作豁免"
msgstr "Manual Action Exemption"

#: apps/backend/constants.py:121 apps/backend/subscription/steps/agent.py:269
#: apps/backend/subscription/steps/agent.py:404
#: apps/backend/subscription/steps/agent.py:584
#: apps/backend/subscription/steps/agent.py:633 apps/node_man/constants.py:293
msgid "重装"
msgstr "Reinstall"

#: apps/backend/constants.py:121
msgid "跨级安装"
msgstr "Cross version installation"

#: apps/backend/constants.py:137
#, fuzzy
msgid "禁用"
msgstr "Deactivate"

#: apps/backend/exceptions.py:21
msgid "文件不存在"
msgstr "File does not exist"

#: apps/backend/exceptions.py:26 apps/node_man/exceptions.py:72
msgid "任务不存在"
msgstr "Task does not exist"

#: apps/backend/exceptions.py:31
msgid "停止调试失败"
msgstr "Failed to stop debugging"

#: apps/backend/exceptions.py:36 apps/backend/subscription/errors.py:120
msgid "插件包不存在"
msgstr "Plugin package does not exist"

#: apps/backend/exceptions.py:37
#, python-brace-format
msgid "插件包[{plugin_name}-{os_type}-{cpu_arch}]不存在"
msgstr "Plugin package [{plugin_name}-{os_type}-{cpu_arch}] does not exist"

#: apps/backend/exceptions.py:42
msgid "插件包状态变更错误"
msgstr "Plugin package state change error"

#: apps/backend/exceptions.py:47
msgid "插件包版本校验错误"
msgstr "Plugin package version check error"

#: apps/backend/exceptions.py:52
msgid "安装命令生成失败"
msgstr "Installation command generation failed"

#: apps/backend/exceptions.py:57
msgid "GSE敏感信息加密失败"
msgstr "GSE sensitive information encryption failed"

#: apps/backend/exceptions.py:62
msgid "插件解析错误"
msgstr "Plugin parsing error"

#: apps/backend/exceptions.py:67
msgid "归档插件包信息错误"
msgstr "Incorrect archive plugin package info"

#: apps/backend/exceptions.py:72
msgid "操作系统版本安装包校验错误"
msgstr "Operating system package version check error"

#: apps/backend/exceptions.py:73
#, python-brace-format
msgid "操作系统[{os_type}]不支持 对应版本[{os_version}]的安装包"
msgstr ""
"The operating system [{os_type}] does not support the installation package "
"of the corresponding version [{os_version}]"

#: apps/backend/exceptions.py:78
msgid "配置模板不存在"
msgstr "Configuration template does not exist"

#: apps/backend/exceptions.py:79
#, python-brace-format
msgid "配置模板[{name}-{filename}-{os_type}-{cpu_arch}]不存在"
msgstr ""
"Configuration template [{name}-{filename}-{os_type}-{cpu_arch}] does not "
"exist"

#: apps/backend/exceptions.py:84
#, python-brace-format
msgid "版本号 -> {version} 不符合语义化版本规则"
msgstr ""
"Version number -> {version} does not comply with semantic versioning rules"

#: apps/backend/healthz/checker/celery_checker.py:114
#: apps/backend/healthz/checker/supervisor_checker.py:86
msgid ", 进程启动时间小于10秒，请关注"
msgstr ""
", The process startup time is less than 10 seconds, please pay attention"

#: apps/backend/healthz/define.py:26
msgid "Redis 状态"
msgstr "Redis Status"

#: apps/backend/healthz/define.py:36
msgid "Redis 可读状态"
msgstr "Redis readable state"

#: apps/backend/healthz/define.py:46
msgid "Redis 可写状态"
msgstr "Redis writable state"

#: apps/backend/healthz/define.py:59
msgid "数据库状态"
msgstr "Database Status"

#: apps/backend/healthz/define.py:72
#, python-format
msgid "rabbitmq 队列长度, 单个队列阀值为%s条"
msgstr "Rabbitmq queue length, the threshold for a single queue is %s"

#: apps/backend/healthz/define.py:86
msgid "celery beat 状态"
msgstr "Celery beat status"

#: apps/backend/healthz/define.py:96
msgid "celery worker 任务执行测试"
msgstr "Celery worker task execution test"

#: apps/backend/healthz/define.py:106
msgid "celery worker 进程状态"
msgstr "Celery worker process status"

#: apps/backend/healthz/define.py:122
msgid "Supervisor 自身状态"
msgstr "Supervisor own state"

#: apps/backend/healthz/define.py:132
msgid "Supervisor 进程状态"
msgstr "Supervisor process status"

#: apps/backend/healthz/define.py:142
msgid "Supervisor 逃逸进程"
msgstr "Supervisor escape process"

#: apps/backend/healthz/define.py:165
#, python-format
msgid "进程：%s未启动或连接不上"
msgstr "Process: %s is not started or cannot be connected"

#: apps/backend/healthz/define.py:166
#, python-format
msgid "确保进程：%s状态正常"
msgstr "Ensure process: %s is healthy"

#: apps/backend/periodic_tasks/check_zombie_sub_inst_record.py:59
#, python-brace-format
msgid ""
"\n"
"[{time_str} ERROR] 任务长时间处在执行状态，已强制失败"
msgstr ""
"\n"
"[{time_str} ERROR] The task has been in the execution state for a long time "
"and has been forced to fail"

#: apps/backend/plugin/handler.py:74 apps/backend/plugin/handler.py:87
#: apps/backend/plugin/views.py:1287
msgid "上传文件MD5校验失败，请确认重试"
msgstr "Upload file MD5 verification failed, please confirm and try again"

#: apps/backend/plugin/handler.py:129 apps/backend/plugin/handler.py:178
#: apps/node_man/views/plugin_v2.py:173
#, python-brace-format
msgid "不存在ID为: {id} 的插件"
msgstr "Plugin with id: {id} does not exist"

#: apps/backend/plugin/manager.py:53
msgid "初始化进程状态"
msgstr "Initializing process state"

#: apps/backend/plugin/manager.py:70
#, python-brace-format
msgid "更新插件部署状态为{status}"
msgstr "Update plugin deployment status is {status}"

#: apps/backend/plugin/manager.py:76 apps/node_man/constants.py:1047
msgid "下发安装包"
msgstr "Transfer installation package"

#: apps/backend/plugin/manager.py:80
msgid "下发脚本"
msgstr "Transfer script"

#: apps/backend/plugin/manager.py:85
msgid "初始化操作脚本"
msgstr "Initialize proc operate script"

#: apps/backend/plugin/manager.py:89 apps/backend/plugin/manager.py:185
msgid "安装插件包"
msgstr "Install plugin package"

#: apps/backend/plugin/manager.py:93
msgid "卸载插件包"
msgstr "Uninstall plugin package"

#: apps/backend/plugin/manager.py:100
msgid "重置重试次数"
msgstr "Reset retries"

#: apps/backend/plugin/manager.py:107
msgid "分配可用端口"
msgstr "Allocate available ports"

#: apps/backend/plugin/manager.py:111
msgid "删除策略"
msgstr "Delete policy"

#: apps/backend/plugin/manager.py:115
msgid "切换订阅启用状态"
msgstr "Toggle subscription enabled state"

#: apps/backend/plugin/manager.py:121
msgid "渲染下发配置"
msgstr "Rendering config"

#: apps/backend/plugin/manager.py:132
#: apps/backend/subscription/steps/plugin.py:1270
msgid "调试插件"
msgstr "Debug plugin"

#: apps/backend/plugin/manager.py:141
#: apps/backend/subscription/steps/plugin.py:1286
msgid "停止调试插件"
msgstr "Stop debugging plugin"

#: apps/backend/plugin/manager.py:153
msgid "操作进程"
msgstr "Operation process"

#: apps/backend/plugin/manager.py:159 apps/node_man/constants.py:310
msgid "移除配置"
msgstr "Remove configuration"

#: apps/backend/plugin/manager.py:184
msgid "下发插件包"
msgstr "Transfer plugin package"

#: apps/backend/plugin/manager.py:186
msgid "下发配置文件"
msgstr "Push config file"

#: apps/backend/plugin/manager.py:187
msgid "执行插件调试进程"
msgstr "Execute plugin debugging process"

#: apps/backend/plugin/manager.py:188
msgid "结束调试并回收资源"
msgstr "End debugging and reclaim resources"

#: apps/backend/plugin/serializers.py:209
msgid "上传端计算的文件md5"
msgstr "File md5 calculated by uploader"

#: apps/backend/plugin/serializers.py:210
msgid "上传端提供的文件名"
msgstr "Filename provided by uploader"

#: apps/backend/plugin/serializers.py:217
msgid "本地文件路径"
msgstr "Local file path"

#: apps/backend/plugin/serializers.py:218
msgid "Nginx所计算的文件md5"
msgstr "File md5 calculated by Nginx"

#: apps/backend/plugin/serializers.py:224
msgid "文件下载url"
msgstr "File download url"

#: apps/backend/plugin/serializers.py:225
msgid "文件保存路径"
msgstr "File save path"

#: apps/backend/plugin/serializers.py:286
msgid "文件名称"
msgstr "Filename"

#: apps/backend/plugin/serializers.py:287
msgid "是否立即发布该插件"
msgstr "Do you want to publish this plugin now"

#: apps/backend/plugin/serializers.py:290
msgid "是否需要读取配置文件"
msgstr "Do you need to read configuration files"

#: apps/backend/plugin/serializers.py:291
msgid "是否可以覆盖已经存在的配置文件"
msgstr "Is it possible to overwrite an existing configuration file"

#: apps/backend/plugin/serializers.py:297
msgid "选择注册的插件包相对路径，缺省默认全选"
msgstr ""
"Select the relative path of the registered plugin package, the default "
"default is to select all"

#: apps/backend/plugin/tasks.py:68
msgid "插件包目录操作无权限"
msgstr "Plugin package directory operation without permission"

#: apps/backend/plugin/tasks.py:74
msgid "插件包上传记录不存在"
msgstr "Plugin package upload record does not exist"

#: apps/backend/plugin/tasks.py:80
msgid "插件包导入失败"
msgstr "Failed to import plugin package"

#: apps/backend/plugin/tools.py:64
#, python-brace-format
msgid "插件不存在: file_path -> {file_path}"
msgstr "Plugin does not exist: file_path -> {file_path}"

#: apps/backend/plugin/tools.py:78
#, python-brace-format
msgid "文件包含非法路径成员 -> {name}，请检查"
msgstr "File contains illegal path member -> {name}, please check"

#: apps/backend/plugin/tools.py:163
msgid "缺少project.yaml文件"
msgstr "Missing project.yaml file"

#: apps/backend/plugin/tools.py:179
msgid "project.yaml文件解析读取失败"
msgstr "Failed to parse and read the project.yaml file"

#: apps/backend/plugin/tools.py:209
msgid "project.yaml 文件信息缺失"
msgstr "Project.yaml file information is missing"

#: apps/backend/plugin/tools.py:229
msgid "project.yaml 中 auto_type 配置异常，请确认后重试"
msgstr ""
"The category configuration in project.yaml is abnormal, please confirm and "
"try again"

#: apps/backend/plugin/tools.py:243
msgid "project.yaml 中 category 配置异常，请确认后重试"
msgstr ""
"The category configuration in project.yaml is abnormal, please confirm and "
"try again"

#: apps/backend/plugin/tools.py:256
msgid "新增插件"
msgstr "Add plugin"

#: apps/backend/plugin/tools.py:266
msgid "已有版本插件更新"
msgstr "Existing version plugin update"

#: apps/backend/plugin/tools.py:276
msgid "低版本插件仅支持导入"
msgstr "Lower version plugins only support import"

#: apps/backend/plugin/tools.py:279
msgid "更新插件版本"
msgstr "Update plugin version"

#: apps/backend/plugin/tools.py:290
#, python-brace-format
msgid ""
"文件路径 -> {pkg_absolute_path} 所在包解析版本为 -> {version}, 最新版本 -> "
"{release_version}, 更新校验失败"
msgstr ""
"File path -> {pkg_absolute_path} where the package resolution version -> "
"{version}, the latest version -> {release_version}, update verification "
"failed"

#: apps/backend/plugin/tools.py:311
#, python-brace-format
msgid "找不到需要导入的配置模板文件 -> {source_path}"
msgstr "Could not find config template file to import -> {source_path}"

#: apps/backend/plugin/tools.py:595
#, python-brace-format
msgid ""
"插件包保存错误，期望保存到 -> {package_target_path}, 实际保存到 -> "
"{storage_path}"
msgstr ""
"Error saving plugin package, expect to save to -> {package_target_path}, "
"actually save to -> {storage_path}"

#: apps/backend/plugin/views.py:112 apps/backend/plugin/views.py:910
msgid "找不到请求发布的文件，请确认后重试"
msgstr "The requested file cannot be found, please confirm and try again"

#: apps/backend/plugin/views.py:166
msgid "找不到请求的任务，请确认后重试"
msgstr "The requested task could not be found, please confirm and try again"

#: apps/backend/plugin/views.py:245
#, python-brace-format
msgid "ID{ids}的插件包未启用，无法执行更改状态操作"
msgstr ""
"Plugin package for ID {ids} is not enabled to perform change state operation"

#: apps/backend/plugin/views.py:723
msgid "调试任务准备中"
msgstr "Debugging task in preparation"

#: apps/backend/plugin/views.py:734
#, python-brace-format
msgid " 开始{name} "
msgstr "Start {name}"

#: apps/backend/plugin/views.py:830 apps/backend/plugin/views.py:1334
msgid "请求任务不存在，请确认后重试"
msgstr "The requested task does not exist, please confirm and try again"

#: apps/backend/plugin/views.py:1278 apps/backend/views.py:219
#: apps/node_man/views/__init__.py:51
#, python-brace-format
msgid "请求参数异常 [{err}]，请确认后重试"
msgstr ""
"The request parameter is abnormal [{err}], please confirm and try again"

#: apps/backend/plugin/views.py:1341
msgid "下载安全校验失败"
msgstr "Download security check failed"

#: apps/backend/serializers/response.py:20 apps/node_man/models.py:931
#: apps/node_man/models.py:1834 apps/node_man/models.py:2228
#: apps/node_man/models.py:2272 apps/node_man/models.py:2447
msgid "订阅ID"
msgstr "Subscription ID"

#: apps/backend/serializers/response.py:24
#: apps/backend/serializers/response.py:31
#: apps/backend/serializers/response.py:43
#: apps/backend/serializers/response.py:70 apps/node_man/handlers/meta.py:256
#: apps/node_man/models.py:2271 apps/node_man/serializers/debug.py:21
#: apps/node_man/serializers/job.py:67
msgid "任务ID"
msgstr "Task ID"

#: apps/backend/serializers/response.py:54
msgid "实例记录数量总和"
msgstr "The total number of subscription instance records"

#: apps/backend/serializers/response.py:55
msgid "实例状态列表"
msgstr "Instance status table"

#: apps/backend/serializers/response.py:56
msgid "订阅全局状态统计"
msgstr "Subscription global status statistics"

#: apps/backend/serializers/response.py:63
msgid "启停动作"
msgstr "Start and stop action"

#: apps/backend/serializers/views.py:20
msgid "下载文件名，只能下载script_tools下的文件"
msgstr "Download file name, you can only download the file under script_tools"

#: apps/backend/subscription/errors.py:26
msgid "订阅不存在"
msgstr "Subscription does not exist"

#: apps/backend/subscription/errors.py:27
#, python-brace-format
msgid "订阅({subscription_id})不存在"
msgstr "Subscription ({subscription_id}) does not exist"

#: apps/backend/subscription/errors.py:34
msgid "Actions不能为None"
msgstr "Actions cannot be None"

#: apps/backend/subscription/errors.py:35
msgid "当scope存在时，actions不能空"
msgstr "Actions cannot be empty when scope exists"

#: apps/backend/subscription/errors.py:42
msgid "订阅任务不存在"
msgstr "Subscription task does not exist"

#: apps/backend/subscription/errors.py:43
#, python-brace-format
msgid "订阅任务({task_id})不存在"
msgstr "Subscription task ({task_id}) does not exist"

#: apps/backend/subscription/errors.py:50
msgid "实例存在运行中任务"
msgstr "Instance has running tasks"

#: apps/backend/subscription/errors.py:51
msgid "实例存在运行中任务，避免重复下发"
msgstr "There are running tasks in the instance, avoid repeated delivery"

#: apps/backend/subscription/errors.py:58
msgid "配置文件渲染失败"
msgstr "Config template file rendering failed"

#: apps/backend/subscription/errors.py:59
#, python-brace-format
msgid "配置文件[{name}]渲染失败，原因：{msg}"
msgstr "Failed to render configuration file [{name}], reason: {msg}"

#: apps/backend/subscription/errors.py:66
msgid "Pipeline任务执行失败"
msgstr "Pipeline task execution failed"

#: apps/backend/subscription/errors.py:67
#, python-brace-format
msgid "Pipeline任务执行失败，原因：{msg}"
msgstr "Pipeline task execution failed, reason: {msg}"

#: apps/backend/subscription/errors.py:74
#: apps/backend/subscription/errors.py:75
msgid "Pipeline任务树解析失败"
msgstr "Pipeline task tree parsing failed"

#: apps/backend/subscription/errors.py:82
#: apps/backend/subscription/errors.py:83
msgid "订阅任务实例不存在"
msgstr "Subscription task instance does not exist"

#: apps/backend/subscription/errors.py:90
msgid "Pipeline任务重复执行"
msgstr "Pipeline task repeated"

#: apps/backend/subscription/errors.py:91
msgid "Pipeline任务已经开始执行，不能重复执行"
msgstr "Pipeline task has already started and cannot be repeated"

#: apps/backend/subscription/errors.py:98
msgid "订阅任务实例为空"
msgstr "Subscribe task instance is empty"

#: apps/backend/subscription/errors.py:99
msgid "订阅任务实例为空，不再创建订阅任务"
msgstr ""
"Subscription task instance is empty, no more subscription tasks will be "
"created"

#: apps/backend/subscription/errors.py:106
msgid "插件校验错误"
msgstr "Plugin validation error"

#: apps/backend/subscription/errors.py:107
#: apps/backend/subscription/errors.py:115
#: apps/backend/subscription/errors.py:121
#: apps/backend/subscription/errors.py:163
#, python-brace-format
msgid "{msg}"
msgstr "{msg}"

#: apps/backend/subscription/errors.py:114
msgid "不允许同一个请求传不同目标"
msgstr "Do not allow the same request to pass different targets"

#: apps/backend/subscription/errors.py:128
msgid "订阅步骤不存在"
msgstr "Subscription step does not exist"

#: apps/backend/subscription/errors.py:129
#, python-brace-format
msgid "订阅步骤({step_id})不存在"
msgstr "Subscription step ({step_id}) does not exist"

#: apps/backend/subscription/errors.py:134
msgid "创建任务失败"
msgstr "Failed to create task"

#: apps/backend/subscription/errors.py:135
#, python-brace-format
msgid "创建任务失败: {err_msg}"
msgstr "Failed to create task: {err_msg}"

#: apps/backend/subscription/errors.py:140
msgid "订阅任务未准备就绪，请稍后再尝试查询"
msgstr "Subscription task not ready, please try query later"

#: apps/backend/subscription/errors.py:141
#, python-brace-format
msgid "订阅任务未准备就绪，请稍后再尝试查询。task_id_list: {task_id_list}"
msgstr ""
"Subscription task is not ready, please try querying later. task_id_list: "
"{task_id_list}"

#: apps/backend/subscription/errors.py:146
msgid "不存在运行中的任务，请确认"
msgstr "No running tasks, please confirm"

#: apps/backend/subscription/errors.py:147
#, python-brace-format
msgid "订阅[id:{subscription_id}]不存在运行中的任务，请确认"
msgstr ""
"Subscription [id:{subscription_id}] has no running tasks, please confirm"

#: apps/backend/subscription/errors.py:154
msgid "订阅更新错误"
msgstr "Subscription update error"

#: apps/backend/subscription/errors.py:155
#, python-brace-format
msgid "订阅[id:{subscription_id}]更新错误: {msg}"
msgstr "Subscription[id:{subscription_id}]update error: {msg}"

#: apps/backend/subscription/errors.py:162
msgid "插件操作脚本校验错误"
msgstr "Plugin operate script validation error"

#: apps/backend/subscription/errors.py:170
#: apps/backend/subscription/errors.py:171
msgid "订阅任务包含Gse2.0灰度业务，任务将暂缓执行无需重复点击"
msgstr ""
"The subscription task includes Gse2.0 grayscale business, the task will be "
"suspended without repeated clicks"

#: apps/backend/subscription/handler.py:104
#, python-brace-format
msgid "订阅[{subscription_id}] 不存在关联的任务"
msgstr "Subscription [{subscription_id}] has no associated tasks"

#: apps/backend/subscription/handler.py:224
#, python-brace-format
msgid "订阅 -> [{subscription_id}] 对应的订阅任务 -> [{task_id_list}] 不存在"
msgstr ""
"The subscription -> [{subscription_id}] associated tasks -> [{task_id_list}] "
"dose not exist"

#: apps/backend/subscription/steps/adapter.py:234
#, python-brace-format
msgid "插件包 [{name}-{version}] 不存在"
msgstr "Plugin package [{name}-{version}] does not exist"

#: apps/backend/subscription/steps/adapter.py:337
#, python-brace-format
msgid "订阅安装插件[{plugin_name}] 未选择支持 [{os_key}] 的插件包"
msgstr ""
"Subscription install plugin [{plugin_name}] has not selected a plugin "
"package that supports [{os_key}]"

#: apps/backend/subscription/steps/adapter.py:352
#, python-brace-format
msgid ""
"插件 [{name}] 不支持 系统:{os_type}-架构:{cpu_arch}-版本:{plugin_version}"
msgstr ""
"Plugin [{name}] does not support system:{os_type}-arch:{cpu_arch}-version:"
"{plugin_version}"

#: apps/backend/subscription/steps/adapter.py:361
#, python-brace-format
msgid ""
"插件 [{name}] 系统:{os_type}-架构:{cpu_arch}-版本:{plugin_version} 未启用"
msgstr ""
"Plugin [{name}] system:{os_type}-arch:{cpu_arch}-version:{plugin_version} is "
"not enabled"

#: apps/backend/subscription/steps/agent.py:306
#: apps/backend/subscription/steps/agent.py:447 apps/node_man/constants.py:292
msgid "升级"
msgstr "Upgrade"

#: apps/backend/subscription/steps/agent.py:351
#: apps/backend/subscription/steps/agent.py:384
#: apps/backend/subscription/steps/agent.py:426
#: apps/backend/subscription/steps/agent.py:509
#: apps/backend/subscription/steps/agent.py:613
msgid "查询Proxy状态"
msgstr "Query Proxy Status"

#: apps/backend/subscription/steps/agent.py:476 apps/node_man/constants.py:291
msgid "替换"
msgstr "Replace"

#: apps/backend/subscription/steps/agent.py:522 apps/node_man/constants.py:247
#: apps/node_man/constants.py:249
msgid "重载配置"
msgstr "Reload configuration"

#: apps/backend/subscription/steps/agent.py:643 apps/node_man/constants.py:248
#: apps/node_man/constants.py:301
msgid "切换配置"
msgstr "Activate agent"

#: apps/backend/subscription/steps/plugin.py:996
msgid "部署插件"
msgstr "Install plugin"

#: apps/backend/subscription/steps/plugin.py:1019
msgid "部署插件程序"
msgstr "Install plugin"

#: apps/backend/subscription/steps/plugin.py:1053
msgid "卸载插件"
msgstr "Uninstall plugin"

#: apps/backend/subscription/steps/plugin.py:1072
#: apps/node_man/constants.py:251
msgid "下发插件配置"
msgstr "Push plugin config"

#: apps/backend/subscription/steps/plugin.py:1092
#: apps/node_man/constants.py:252
msgid "移除插件配置"
msgstr "Remove plugin configuration"

#: apps/backend/subscription/steps/plugin.py:1110
msgid "启动插件进程"
msgstr "Start plugin"

#: apps/backend/subscription/steps/plugin.py:1138
msgid "重启插件进程"
msgstr "Restart plugin"

#: apps/backend/subscription/steps/plugin.py:1163
msgid "停止插件进程"
msgstr "Stop plugin"

#: apps/backend/subscription/steps/plugin.py:1197
#: apps/node_man/constants.py:242
msgid "重载插件"
msgstr "Reload plugin"

#: apps/backend/subscription/steps/plugin.py:1224
#: apps/node_man/constants.py:243
msgid "托管插件"
msgstr "Supervised plugin"

#: apps/backend/subscription/steps/plugin.py:1252
#: apps/node_man/constants.py:244
msgid "取消托管插件"
msgstr "Cancel supervised plugin"

#: apps/backend/subscription/steps/plugin.py:1298
#: apps/backend/subscription/steps/plugin.py:1316
msgid "停用插件并删除订阅"
msgstr "Stop plugin and delete policy"

#: apps/backend/subscription/tasks.py:98
#, python-brace-format
msgid "[{step_id}] {action_description}"
msgstr "[{step_id}] {action_description}"

#: apps/backend/subscription/tasks.py:362
#, python-brace-format
msgid ""
"当前{category_alias}（{bk_obj_name} 级）已被优先级更高的"
"{suppressed_by_category_alias}【{suppressed_by_name}(ID: "
"{suppressed_by_id})】（{suppressed_by_obj_name} 级）抑制"
msgstr ""
"The current {category_alias} ({bk_obj_name} level) has been replaced by a "
"higher policy{suppressed_by_category_alias}[{suppressed_by_name}(ID: "
"{suppressed_by_id})] ({suppressed_by_obj_name} level) suppressed"

#: apps/backend/subscription/views.py:246
#, python-brace-format
msgid "新增订阅步骤[{step_id}] 需要提供 type & config，错误信息 -> {err_msg}"
msgstr ""
"Add subscription step [{step_id}] need to provide type & config, error "
"message -> {err_msg}"

#: apps/backend/sync_task/constants.py:24
msgid "同步 CMDB 主机数据"
msgstr "Sync CMDB hosts"

#: apps/backend/sync_task/serializers.py:29
#: apps/node_man/serializers/sync_task.py:24
msgid "不支持的任务名称: {}"
msgstr "Unsupported task name: {}"

#: apps/backend/utils/ssh.py:191
msgid "认证方式错误或不支持，请确认。"
msgstr "The authentication method is wrong or not supported, please confirm."

#: apps/backend/utils/ssh.py:202
msgid "HostKey校验失败，请尝试删除 /root/.ssh/known_hosts 再重试"
msgstr ""
"HostKey verification failed, please try to delete /root/.ssh/known_hosts and "
"try again"

#: apps/backend/utils/ssh.py:213
msgid "登录认证失败，请确认账号，密码，密钥或IP是否正确。"
msgstr ""
"Sign in authentication failed, please confirm the account, password, key or "
"IP is correct."

#: apps/backend/utils/ssh.py:219
msgid "ssh登录，请确认IP是否正确或目标机器是否可被正常登录。"
msgstr ""
"Ssh sign in, please confirm whether the IP is correct or whether the target "
"machine can be logged in normally."

#: apps/backend/utils/ssh.py:225
msgid ""
"ssh登录连接超时，请确认IP是否正确或ssh端口号是否正确或网络策略是否正确开通。"
msgstr ""
"The ssh sign in connection timed out, please confirm whether the IP is "
"correct or the ssh port number is correct or the network policy is correctly "
"enabled."

#: apps/core/concurrent/controller.py:37
#, python-brace-format
msgid "解析并发控制配置失败，报错原因 -> {err}"
msgstr ""
"Failed to parse concurrency control configuration, error reason -> {err}"

#: apps/core/concurrent/serializers.py:20
msgid "是否全量执行"
msgstr "Whether full execution"

#: apps/core/concurrent/serializers.py:23
msgid "每批任务的执行数量限制"
msgstr "The limit on the number of tasks to execute per batch"

#: apps/core/concurrent/serializers.py:29
msgid "批次间是否并发执行"
msgstr "Whether batches are executed concurrently"

#: apps/core/concurrent/serializers.py:34
msgid "任务提交间隔"
msgstr "Task submit interval"

#: apps/core/encrypt/constants.py:25
msgid "公钥"
msgstr "Public key"

#: apps/core/encrypt/constants.py:25
msgid "私钥"
msgstr "Private key"

#: apps/core/encrypt/constants.py:33
msgid "默认RSA密钥"
msgstr "Default RSA key"

#: apps/core/encrypt/models.py:19 apps/core/encrypt/serializers.py:18
msgid "密钥名称"
msgstr "Key name"

#: apps/core/encrypt/models.py:20
msgid "密钥类型"
msgstr "Key Type"

#: apps/core/encrypt/models.py:21
msgid "密钥描述"
msgstr "Key description"

#: apps/core/encrypt/models.py:22
msgid "密钥内容"
msgstr "Key Content"

#: apps/core/encrypt/models.py:24 apps/core/files/models.py:27
#: apps/core/files/models.py:46 apps/node_man/models.py:333
#: apps/node_man/models.py:379 apps/node_man/models.py:1658
#: apps/node_man/models.py:1913 apps/node_man/models.py:2278
#: apps/node_man/models.py:2436 apps/utils/orm.py:176
msgid "更新时间"
msgstr "Update time"

#: apps/core/encrypt/models.py:25 apps/core/files/models.py:28
#: apps/core/files/models.py:47 apps/node_man/models.py:378
#: apps/node_man/models.py:1657 apps/node_man/models.py:1683
#: apps/node_man/models.py:1767 apps/node_man/models.py:1914
#: apps/node_man/models.py:2231 apps/node_man/models.py:2279
#: apps/node_man/models.py:2416 apps/node_man/models.py:2437
#: apps/node_man/models.py:2453 apps/node_man/models.py:2496
#: apps/utils/orm.py:174 pipeline/engine/models/core.py:882
#: pipeline/models.py:78 pipeline/models.py:275 pipeline/models.py:617
msgid "创建时间"
msgstr "Created at"

#: apps/core/encrypt/models.py:28 apps/core/encrypt/models.py:29
msgid "RSA密钥"
msgstr "RSA key"

#: apps/core/encrypt/serializers.py:18
msgid "密钥名称列表"
msgstr "List of key names"

#: apps/core/files/base.py:109
msgid "未知文件源"
msgstr "Unknown file source"

#: apps/core/files/base.py:112
#, python-brace-format
msgid "{file_type_alias}-{file_source_alias}"
msgstr "{file_type_alias}-{file_source_alias}"

#: apps/core/files/base.py:120
#, python-brace-format
msgid "{file_type_alias}-{server_ips_str}"
msgstr "{file_type_alias}-{server_ips_str}"

#: apps/core/files/base.py:124
#, python-brace-format
msgid "未知文件源-{file_type}"
msgstr "Unknown file source - {file_type}"

#: apps/core/files/base.py:127
#, python-brace-format
msgid ""
"从 [{source_info_str}] 下发文件 [{file_list_str}] 到目标机器路径 "
"[{file_target_path}]"
msgstr ""
"Transfer file [{file_list_str}] from [{source_info_str}] to target machine "
"path [{file_target_path}]"

#: apps/core/files/base.py:216
#, python-brace-format
msgid "文件分发错误：err_msg -> {err_msg}"
msgstr "File distribution error: err_msg -> {err_msg}"

#: apps/core/files/constants.py:29
msgid "私有仓库"
msgstr "Private repository"

#: apps/core/files/constants.py:29
msgid "公共仓库"
msgstr "Public repository"

#: apps/core/files/constants.py:40
msgid "服务器文件"
msgstr "Server file"

#: apps/core/files/constants.py:40
msgid "第三方文件源文件"
msgstr "Third party file source file"

#: apps/core/files/constants.py:55
msgid "蓝鲸制品库"
msgstr "BKREPO"

#: apps/core/files/constants.py:55
msgid "本地文件系统"
msgstr "Local filesystem"

#: apps/core/files/exceptions.py:22
msgid "注册凭证失败"
msgstr "Failed to register credentials"

#: apps/core/files/exceptions.py:27
msgid "文件源类型错误"
msgstr "File source type error"

#: apps/core/files/exceptions.py:32
msgid "注册文件失败"
msgstr "Failed to register file"

#: apps/core/files/exceptions.py:37 apps/node_man/constants.py:734
msgid "文件传输失败"
msgstr "File transfer failed"

#: apps/core/files/file_source.py:25
#, python-brace-format
msgid "storage_type must be one of {choices}"
msgstr "storage_type must be one of {choices}"

#: apps/core/files/file_source.py:33
#, python-brace-format
msgid "节点管理[{bk_biz_id}]{storage_type_alias}文件凭证"
msgstr "Node management [{bk_biz_id}]{storage_type_alias} file credential"

#: apps/core/files/file_source.py:92
#, python-brace-format
msgid "注册凭证失败：bk_biz_id -> {bk_biz_id}, err_msg -> {err_msg}"
msgstr ""
"Failed to register credentials: bk_biz_id -> {bk_biz_id}, err_msg -> "
"{err_msg}"

#: apps/core/files/file_source.py:145
#, python-brace-format
msgid "节点管理[{bk_biz_id}]{storage_type_alias}文件源"
msgstr "Node management [{bk_biz_id}]{storage_type_alias} file source"

#: apps/core/files/file_source.py:200
#, python-brace-format
msgid "注册文件源失败：bk_biz_id -> {bk_biz_id}, err_msg -> {err_msg}"
msgstr ""
"Failed to register file source: bk_biz_id -> {bk_biz_id}, err_msg -> "
"{err_msg}"

#: apps/core/files/models.py:19 apps/core/tag/models.py:59
#: apps/node_man/models.py:349 apps/node_man/models.py:1654
#: apps/node_man/models.py:1907 apps/node_man/serializers/base.py:100
#: apps/node_man/serializers/cmdb.py:39 apps/node_man/serializers/cmdb.py:44
#: apps/node_man/serializers/host.py:32 apps/node_man/serializers/host.py:71
#: apps/node_man/serializers/host.py:91 apps/node_man/serializers/job.py:70
#: apps/node_man/serializers/job.py:86 apps/node_man/serializers/job.py:277
#: apps/node_man/serializers/plugin.py:226
#: apps/node_man/serializers/plugin_v2.py:235
#: apps/node_man/serializers/policy.py:114
#: apps/node_man/serializers/policy.py:138
msgid "业务ID"
msgstr "Business ID"

#: apps/core/files/models.py:20
msgid "文件源存储类型"
msgstr "File source storage type"

#: apps/core/files/models.py:22
msgid "凭证名称"
msgstr "Credential name"

#: apps/core/files/models.py:23
msgid "凭证类型"
msgstr "Credential type"

#: apps/core/files/models.py:24
msgid "凭证描述"
msgstr "Credential description"

#: apps/core/files/models.py:25 apps/core/files/models.py:38
msgid "凭证ID"
msgstr "Credential ID"

#: apps/core/files/models.py:31 apps/core/files/models.py:32
msgid "作业平台文件凭证"
msgstr "Job platform file credentials"

#: apps/core/files/models.py:39
msgid "文件源ID"
msgstr "File source ID"

#: apps/core/files/models.py:41 apps/core/files/models.py:44
msgid "文件源标识"
msgstr "File source ID"

#: apps/core/files/models.py:42
msgid "文件源别名"
msgstr "File source alias"

#: apps/core/files/models.py:43
msgid "文件源接入参数"
msgstr "File Source Access Parameters"

#: apps/core/files/models.py:62 apps/core/files/models.py:63
msgid "作业平台文件源"
msgstr "JOB file source"

#: apps/core/gray/handlers.py:59
#, python-brace-format
msgid "cloud_ip [{cloud_ip}] 格式输入有误, 请修改后重试 示例: 0:127.0.0.1"
msgstr ""
"cloud_ip [{cloud_ip}] The input format is incorrect, please modify and try "
"again Example: 0:127.0.0.1"

#: apps/core/gray/handlers.py:90
msgid "请联系管理员配置GSE1.0-2.0接入点映射"
msgstr ""
"Please contact the administrator to configure GSE1.0-2.0 access point mapping"

#: apps/core/gray/handlers.py:285
#, python-brace-format
msgid ""
"缺少「管控区域」 -> {bk_cloud_name} ID -> {bk_cloud_id}, 接入点版本的映射关"
"系，请联系管理员"
msgstr ""
"The mapping relationship between \"BK-Net\" -> {bk_cloud_name} ID -> "
"{bk_cloud_id}, access point version is missing, please contact the "
"administrator"

#: apps/core/gray/permission.py:19 apps/core/ipchooser/permission.py:57
#: apps/core/ipchooser/permission.py:93 apps/core/tag/permission.py:23
#: apps/node_man/handlers/permission.py:200
#: apps/node_man/handlers/permission.py:261
msgid "您没有该操作的权限"
msgstr "You do not have permission for this operation"

#: apps/core/gray/serializers.py:17
msgid "业务列表"
msgstr "Business list"

#: apps/core/gray/serializers.py:21
msgid "管控区域:主机列表"
msgstr "cloud_id:ip list"

#: apps/core/gray/serializers.py:25
msgid "失败的主机"
msgstr "Failed Hosts"

#: apps/core/gray/serializers.py:26
msgid "成功的主机"
msgstr "Successed Hosts"

#: apps/core/gray/serializers.py:27
msgid "无bk_agent_id的主机"
msgstr "No bk_agent_id hosts"

#: apps/core/ipchooser/constants.py:38
msgid "字段分隔符"
msgstr "Field separation symbol"

#: apps/core/ipchooser/constants.py:39
msgid "全量返回标志"
msgstr "Full return sign"

#: apps/core/ipchooser/constants.py:40
msgid "默认模糊查询字段"
msgstr "Default fuzzy query field"

#: apps/core/ipchooser/constants.py:41
msgid "主机列表默认返回字段"
msgstr "The host list defaults return field"

#: apps/core/ipchooser/constants.py:52 apps/core/ipchooser/constants.py:64
#: apps/node_man/constants.py:787 apps/node_man/constants.py:983
msgid "业务"
msgstr "Business"

#: apps/core/ipchooser/constants.py:64 apps/node_man/constants.py:983
msgid "集群"
msgstr "Cluster"

#: apps/core/ipchooser/constants.py:64 apps/node_man/constants.py:983
msgid "模块"
msgstr "Module"

#: apps/core/ipchooser/constants.py:75
msgid "存活"
msgstr "Surviving"

#: apps/core/ipchooser/constants.py:75
msgid "未存活"
msgstr "Not surviving"

#: apps/core/ipchooser/exceptions.py:22 apps/core/ipchooser/exceptions.py:23
msgid "参数校验失败"
msgstr "Parameter verification failed"

#: apps/core/ipchooser/exceptions.py:28
#, python-brace-format
msgid "业务【bk_biz_id: {bk_biz_id}】拓扑不存在"
msgstr "Business [BK_BIZ_ID: {bk_biz_id}] topology does not exist"

#: apps/core/ipchooser/exceptions.py:29
msgid "业务拓扑不存在"
msgstr "Business topology does not exist"

#: apps/core/ipchooser/query/resource.py:43
msgid "资源池"
msgstr "Resource pool"

#: apps/core/ipchooser/serializers/base.py:25
msgid "权限类型"
msgstr "Permission type"

#: apps/core/ipchooser/serializers/base.py:40
msgid "数据起始位置"
msgstr "Data Start Location"

#: apps/core/ipchooser/serializers/base.py:42
#: apps/node_man/serializers/base.py:89
msgid "拉取数据数量，不传或传 `-1` 表示拉取所有"
msgstr ""
"Pull the number of data, not pass or pass or passed` -1` indicate all the "
"pulling all "

#: apps/core/ipchooser/serializers/base.py:51
msgid "资源范围类型"
msgstr "Resource scope type"

#: apps/core/ipchooser/serializers/base.py:52
msgid "资源范围ID"
msgstr "Resource scope ID"

#: apps/core/ipchooser/serializers/base.py:54
#: apps/core/ipchooser/serializers/base.py:62
msgid "业务 ID"
msgstr "Business ID"

#: apps/core/ipchooser/serializers/base.py:69
msgid "节点类型ID"
msgstr "Node type ID"

#: apps/core/ipchooser/serializers/base.py:70
msgid "节点实例ID"
msgstr "Node instance ID"

#: apps/core/ipchooser/serializers/base.py:75 apps/node_man/models.py:354
#: apps/node_man/serializers/ap.py:77 apps/node_man/serializers/host.py:38
#: apps/node_man/serializers/job.py:97
msgid "内网IP"
msgstr "Intranet IP"

#: apps/core/ipchooser/serializers/base.py:76 apps/node_man/models.py:359
#: apps/node_man/serializers/ap.py:78 apps/node_man/serializers/host.py:39
#: apps/node_man/serializers/job.py:101
msgid "内网IPv6"
msgstr "Intranet IPv6"

#: apps/core/ipchooser/serializers/base.py:77
msgid "操作系统类型"
msgstr "Operating system type"

#: apps/core/ipchooser/serializers/base.py:78
#: apps/node_man/handlers/meta.py:178 apps/node_man/handlers/meta.py:351
#: apps/node_man/models.py:351
msgid "主机名称"
msgstr "Hostname"

#: apps/core/ipchooser/serializers/base.py:79
#: apps/node_man/serializers/cloud.py:28
msgid "管控区域名称"
msgstr "BK-Net name"

#: apps/core/ipchooser/serializers/base.py:81
msgid "Agent 存活状态"
msgstr "Agent alive status"

#: apps/core/ipchooser/serializers/base.py:84
msgid ""
"模糊搜索内容（支持同时对`主机IP`/`主机名`/`操作系统`/`管控区域名称`进行模糊搜"
"索"
msgstr ""
"Vague search content (support at the same time of` host ip`/`host name`/"
"`operating system`/`` BK-Net name `)"

#: apps/core/ipchooser/serializers/base.py:90
msgid "主机 ID 列表"
msgstr "Host ID List"

#: apps/core/ipchooser/serializers/base.py:90
#: apps/core/ipchooser/serializers/base.py:96
msgid "主机 ID"
msgstr "Host ID"

#: apps/core/ipchooser/serializers/base.py:92
msgid "节点列表"
msgstr "Node list"

#: apps/core/ipchooser/serializers/base.py:96
msgid "限制检索的主机 ID 列表"
msgstr "List of host ID to limit retrieval"

#: apps/core/ipchooser/serializers/base.py:128
msgid "是否获取所有资源范围的拓扑结构，默认为 `false`"
msgstr ""
"Whether to get the topology of all resource scopes, default to` false` "

#: apps/core/ipchooser/serializers/base.py:129
msgid "要获取拓扑结构的资源范围数组"
msgstr "Array of resource scopes to get topology"

#: apps/core/ipchooser/serializers/base.py:136
msgid "模糊搜索内容"
msgstr "Vague search content"

#: apps/core/ipchooser/serializers/base.py:139
#: apps/node_man/serializers/base.py:102 apps/node_man/serializers/host.py:73
#: apps/node_man/serializers/job.py:279 apps/node_man/serializers/job.py:389
#: apps/node_man/serializers/plugin.py:233
#: apps/node_man/serializers/policy.py:116
#: apps/node_man/serializers/policy.py:140
msgid "搜索条件"
msgstr "Search Criteria"

#: apps/core/ipchooser/serializers/base.py:192
msgid "管控区域 ID"
msgstr "BK-Net ID"

#: apps/core/ipchooser/serializers/base.py:193
msgid "IPv4 协议下的主机IP"
msgstr "Host IP under IPv4 protocol"

#: apps/core/ipchooser/serializers/base.py:194
msgid "主机 ID，优先取 `host_id`，否则取 `ip` + `cloud_id`"
msgstr "Host ID, take ` host_id` first, otherwise take the `ip` +` cloud_id`"

#: apps/core/ipchooser/serializers/base.py:198
msgid "请传入 host_id 或者 cloud_id + ip"
msgstr "Please incoming `host_id` or `cloud_id + ip`"

#: apps/core/ipchooser/serializers/host_sers.py:20
msgid "IPv4 列表"
msgstr "IPv4 List"

#: apps/core/ipchooser/serializers/host_sers.py:21
msgid "IPv4，支持的输入格式：`cloud_id:ip` / `ip`"
msgstr "IPv4, supported input format: `cloud_id:ip` / `ip`"

#: apps/core/ipchooser/serializers/host_sers.py:26
msgid "IPv6 列表"
msgstr "IPv6 list"

#: apps/core/ipchooser/serializers/host_sers.py:27
msgid "IPv6，支持的输入格式：`cloud_id:ipv6` / `ipv6`"
msgstr "IPv6, supported input format:` cloud_id: ipv6` / `ipv6`"

#: apps/core/ipchooser/serializers/host_sers.py:32
msgid "关键字列表"
msgstr "Keyword list"

#: apps/core/ipchooser/serializers/host_sers.py:33
msgid "关键字，解析出的`主机名`、`host_id` 等关键字信息"
msgstr ""
"Keywords, resolving keyword information such as the main name`, `host_id`"

#: apps/core/ipchooser/serializers/host_sers.py:37
#: apps/core/ipchooser/serializers/topo_sers.py:43
msgid "检索范围限制"
msgstr "Restriction range restrictions"

#: apps/core/ipchooser/views.py:32
msgid "批量获取含各节点主机数量的拓扑树"
msgstr "Get the topology tree with the number of hosts on each node in batches"

#: apps/core/ipchooser/views.py:42
msgid "查询多个节点拓扑路径"
msgstr "Query multiple node topology paths"

#: apps/core/ipchooser/views.py:52
msgid "根据多个拓扑节点与搜索条件批量分页查询所包含的主机信息"
msgstr ""
"The host information contained in batch pagination search in accordance with "
"multiple topology nodes and search conditions"

#: apps/core/ipchooser/views.py:70
msgid "根据多个拓扑节点与搜索条件批量分页查询所包含的主机 ID 信息"
msgstr ""
"The host ID information contained in batch pagination search in accordance "
"with multiple topology nodes and search conditions"

#: apps/core/ipchooser/views.py:88
msgid "获取多个拓扑节点的主机 Agent 状态统计信息"
msgstr "Get host Agent status statistics for multiple topology nodes"

#: apps/core/ipchooser/views.py:104
msgid ""
"根据用户手动输入的`IP`/`IPv6`/`主机名`/`host_id`等关键字信息获取真实存在的机"
"器信息"
msgstr ""
"Obtain real machine information based on keyword information such as `IP`/"
"`IPv6`/`hostname`/`host_id` manually entered by the user"

#: apps/core/ipchooser/views.py:122
msgid "根据主机关键信息获取机器详情信息"
msgstr "Obtain machine detailed information based on host keyword information"

#: apps/core/remote/conns/asyncssh_impl.py:72
#: apps/core/remote/conns/paramiko_impl.py:88
#, python-brace-format
msgid "连接超时：{err_msg}"
msgstr "Connection timed out: {err_msg}"

#: apps/core/remote/conns/asyncssh_impl.py:86
#, python-brace-format
msgid "命令执行超时：{err_msg}"
msgstr "Command execution timed out: {err_msg}"

#: apps/core/remote/conns/asyncssh_impl.py:93
#, python-brace-format
msgid "exit_status -> {exit_status}, stdout -> {stdout}, stderr -> {stderr}"
msgstr "exit_status -> {exit_status}, stdout -> {stdout}, stderr -> {stderr}"

#: apps/core/remote/conns/paramiko_impl.py:65
#, python-brace-format
msgid "密钥验证失败：{err_msg}"
msgstr "Key verification failed: {err_msg}"

#: apps/core/remote/exceptions.py:22
#, python-brace-format
msgid "命令执行失败: {err_msg}"
msgstr "Command execution failed: {err_msg}"

#: apps/core/remote/exceptions.py:23
msgid "命令执行失败"
msgstr "Command execution failed"

#: apps/core/remote/exceptions.py:28
#, python-brace-format
msgid "密钥交换失败: {err_msg}"
msgstr "Key exchange failed: {err_msg}"

#: apps/core/remote/exceptions.py:33
#, python-brace-format
msgid "认证失败，请检查认证信息是否有误：{err_msg}"
msgstr ""
"Authentication failed, please check the authentication information for "
"errors: {err_msg}"

#: apps/core/remote/exceptions.py:38
#, python-brace-format
msgid "连接丢失，远程系统未响应或登录超时：{err_msg}"
msgstr ""
"Connection lost, remote system not responding or sign in timed out: {err_msg}"

#: apps/core/remote/exceptions.py:43
#, python-brace-format
msgid "远程连接失败：{err_msg}"
msgstr "Remote connection failed: {err_msg}"

#: apps/core/remote/exceptions.py:48
msgid "超时"
msgstr "Timeout"

#: apps/core/remote/exceptions.py:53
#, python-brace-format
msgid "命令返回非零值：{err_msg}"
msgstr "Command returned non-zero value: {err_msg}"

#: apps/core/remote/exceptions.py:58
#, python-brace-format
msgid "会话异常：{err_msg}"
msgstr "Session exception: {err_msg}"

#: apps/core/remote/exceptions.py:63
#, python-brace-format
msgid "IO 异常：{err_msg}"
msgstr "IO exception: {err_msg}"

#: apps/core/remote/exceptions.py:68
#, python-brace-format
msgid "文件远程客户端异常：{err_msg}"
msgstr "File remote client exception: {err_msg}"

#: apps/core/script_manage/data.py:24
msgid "关闭 Windows 防火墙"
msgstr "Close Windows Firewall"

#: apps/core/script_manage/data.py:33
msgid "开通 Windows Agent 2.0 安装所需的网络策略"
msgstr "Open Windows Agent 2.0 installation required network polices"

#: apps/core/tag/constants.py:27 apps/node_man/constants.py:159
msgid "插件"
msgstr "Plugin"

#: apps/core/tag/constants.py:27 apps/node_man/constants.py:159
msgid "Agent"
msgstr "Agent"

#: apps/core/tag/constants.py:38
msgid "删除标签"
msgstr "Delete tag"

#: apps/core/tag/constants.py:38
msgid "新建标签"
msgstr "New tag"

#: apps/core/tag/constants.py:38
msgid "更新版本"
msgstr "Updated version"

#: apps/core/tag/constants.py:38
msgid "同版本覆盖更新"
msgstr "Same version coverage update"

#: apps/core/tag/exceptions.py:22
#, python-brace-format
msgid "目标不存在：target_id -> {target_id}"
msgstr "The target does not exist: target_id -> {target_id}"

#: apps/core/tag/exceptions.py:23
msgid "目标不存在"
msgstr "Target does not exist"

#: apps/core/tag/exceptions.py:28
#, python-brace-format
msgid "标签不存在：target_id -> {target_id}, target_type -> {target_type}"
msgstr ""
"The label does not exist: target_id -> {target_id}, target_type -> "
"{target_type}"

#: apps/core/tag/exceptions.py:29
msgid "标签不存在"
msgstr "Tag not exist"

#: apps/core/tag/exceptions.py:34
#, python-brace-format
msgid "发布标签版本错误：{err_msg}"
msgstr "Release the tag version error: {err_msg}"

#: apps/core/tag/exceptions.py:35
msgid "发布标签版本错误"
msgstr "Release the label version error"

#: apps/core/tag/exceptions.py:40
#, python-brace-format
msgid "目标处理器不存在：target_type -> {target_type}"
msgstr "Target Helper dose not exists: target_type -> {target_type}"

#: apps/core/tag/exceptions.py:41
msgid "目标处理器不存在"
msgstr "Target Helper dose not exists"

#: apps/core/tag/exceptions.py:46 apps/core/tag/exceptions.py:47
msgid "标签已存在"
msgstr "Tag already exists"

#: apps/core/tag/exceptions.py:52
#, python-brace-format
msgid "标签名非法：{err_msg}"
msgstr "Illegal tag name: {err_msg}"

#: apps/core/tag/exceptions.py:53
msgid "标签名非法"
msgstr "Illegal tag name"

#: apps/core/tag/models.py:21 apps/core/tag/serializers.py:20
msgid "标签名称"
msgstr "Tag Name"

#: apps/core/tag/models.py:22
msgid "标签是否置顶"
msgstr "Is the tag is on top"

#: apps/core/tag/models.py:23
msgid "标签描述"
msgstr "Tag Description"

#: apps/core/tag/models.py:24 apps/core/tag/models.py:56
#: apps/core/tag/serializers.py:21
msgid "目标类型"
msgstr "Type type"

#: apps/core/tag/models.py:25 apps/core/tag/serializers.py:22
msgid "目标 ID"
msgstr "Target ID"

#: apps/core/tag/models.py:26 apps/core/tag/models.py:38
#: apps/core/tag/serializers.py:23
msgid "指向版本号"
msgstr "Point to the version number"

#: apps/core/tag/models.py:29 apps/core/tag/models.py:30
msgid "标签"
msgstr "Tag"

#: apps/core/tag/models.py:36
msgid "标签 ID"
msgstr "Tag ID"

#: apps/core/tag/models.py:37
msgid "变更动作"
msgstr "Change action"

#: apps/core/tag/models.py:41 apps/core/tag/models.py:42
msgid "标签变更记录"
msgstr "Tag change record"

#: apps/core/tag/models.py:54
msgid "构件名称"
msgstr "Component name"

#: apps/core/tag/models.py:55
msgid "构件版本"
msgstr "Component version"

#: apps/core/tag/models.py:57
msgid "是否全部可见"
msgstr "Is all visible"

#: apps/core/tag/models.py:60 apps/node_man/models.py:603
#: apps/node_man/models.py:1655
msgid "CMDB对象ID"
msgstr "CMDB object ID"

#: apps/core/tag/models.py:61
msgid "CMDB实例ID范围"
msgstr "CMDB instance ID range"

#: apps/core/tag/models.py:64 apps/core/tag/models.py:65
msgid "版本可见范围"
msgstr "Version visibility range"

#: apps/core/tag/serializers.py:36
#, python-brace-format
msgid "标签名（{name}）不能与目标版本（{target_version}）一致"
msgstr ""
"Tag name ({name}) cannot be consistent with the target version "
"({target_version})"

#: apps/core/tag/targets/plugin.py:220
#, python-brace-format
msgid "未找到版本包（{helper_info}）"
msgstr "Version package not found （{helper_info}）"

#: apps/exceptions.py:24
msgid "系统异常"
msgstr "System exception"

#: apps/exceptions.py:63 apps/exceptions.py:122
msgid "参数验证失败"
msgstr "Parameter validation failed"

#: apps/exceptions.py:68
msgid "远程服务请求结果异常"
msgstr "The remote service request result was abnormal"

#: apps/exceptions.py:73
msgid "组件调用异常"
msgstr "Component call exception"

#: apps/exceptions.py:78
msgid "业务不存在"
msgstr "Business does not exist"

#: apps/exceptions.py:83
msgid "语言不支持"
msgstr "Language not supported"

#: apps/exceptions.py:88
msgid "权限不足"
msgstr "Insufficient permissions"

#: apps/exceptions.py:94
msgid "服务不稳定，请检查组件健康状况"
msgstr "Service unstable, please check component health"

#: apps/exceptions.py:100
msgid "请升级blueapps至最新版本"
msgstr "Please upgrade blueapps to the latest version"

#: apps/exceptions.py:105
msgid "获取JWT信息异常"
msgstr "Error getting JWT information"

#: apps/exceptions.py:110
msgid "JWT校验失败"
msgstr "JWT validation failed"

#: apps/generic.py:202 apps/middlewares.py:196
#, python-brace-format
msgid "【APP 自定义异常】{message}, code={code}, args={args}"
msgstr "[app custom exception]{message}, code={code}, args={args}"

#: apps/generic.py:220
msgid "系统错误，请联系管理员"
msgstr "System error, please contact administrator"

#: apps/iam/exceptions.py:45
#, python-brace-format
msgid "当前用户无 [{action_name}] 权限"
msgstr "Current user does not have permission for [{action_name}]"

#: apps/iam/handlers/actions.py:174
#, python-brace-format
msgid "动作ID不存在：{action_id}"
msgstr "Action ID does not exist: {action_id}"

#: apps/iam/handlers/permission.py:243
#, python-brace-format
msgid "获取系统信息错误：{message}"
msgstr "Error getting system information: {message}"

#: apps/iam/handlers/permission.py:257 common/api/modules/cc.py:20
msgid "配置平台"
msgstr "CMDB"

#: apps/iam/handlers/resources.py:186
#, python-brace-format
msgid "资源ID不存在：{resource_id}"
msgstr "Resource ID does not exist: {resource_id}"

#: apps/iam/serializers.py:17
msgid "资源类型"
msgstr "Resource type"

#: apps/iam/serializers.py:18
msgid "资源ID"
msgstr "Resource ID"

#: apps/iam/serializers.py:20
msgid "动作ID列表"
msgstr "Action ID List"

#: apps/iam/serializers.py:21
msgid "资源列表"
msgstr "Resource list"

#: apps/middlewares.py:185
msgid "S-mart应用仅支持正式环境部署！"
msgstr "S-mart application only supports production environment deployment!"

#: apps/node_man/constants.py:75 apps/node_man/handlers/cloud.py:108
#: apps/node_man/handlers/meta.py:361 apps/node_man/models.py:853
msgid "直连区域"
msgstr "Direct Mode"

#: apps/node_man/constants.py:159
msgid "Proxy"
msgstr "Proxy"

#: apps/node_man/constants.py:225
msgid "安装 Proxy"
msgstr "Install Proxy"

#: apps/node_man/constants.py:226
msgid "安装 Agent"
msgstr "Install Agent"

#: apps/node_man/constants.py:227
msgid "重启 Agent"
msgstr "Restart Agent"

#: apps/node_man/constants.py:228
msgid "重启 Proxy"
msgstr "Restart Proxy"

#: apps/node_man/constants.py:229
msgid "替换 Proxy"
msgstr "Replace Proxy"

#: apps/node_man/constants.py:230
msgid "重装 Proxy"
msgstr "Reinstall Proxy"

#: apps/node_man/constants.py:231
msgid "重装 Agent"
msgstr "Reinstall Agent"

#: apps/node_man/constants.py:232
msgid "升级 Proxy"
msgstr "Upgrade Proxy"

#: apps/node_man/constants.py:233
msgid "升级 Agent"
msgstr "Upgrade Agent"

#: apps/node_man/constants.py:234
msgid "移除 Agent"
msgstr "Remove Agent"

#: apps/node_man/constants.py:235
msgid "卸载 Agent"
msgstr "Uninstall Agent"

#: apps/node_man/constants.py:236
msgid "卸载 Proxy"
msgstr "Uninstall Proxy"

#: apps/node_man/constants.py:237
msgid "导入Proxy机器"
msgstr "Import proxy machine"

#: apps/node_man/constants.py:238
msgid "导入Agent机器"
msgstr "Import Agent Machine"

#: apps/node_man/constants.py:239
msgid "启动插件"
msgstr "Start plugin"

#: apps/node_man/constants.py:240
msgid "停止插件"
msgstr "Stop plugin"

#: apps/node_man/constants.py:241
msgid "重启插件"
msgstr "Restart plugin"

#: apps/node_man/constants.py:245
msgid "安装插件"
msgstr "Install plugin"

#: apps/node_man/constants.py:246
msgid "停止插件并删除策略"
msgstr "Stop plugin and delete policy"

#: apps/node_man/constants.py:250
msgid "打包插件"
msgstr "Package plugin"

#: apps/node_man/constants.py:254
msgid "手动安装 Agent"
msgstr "Manually install the agent"

#: apps/node_man/constants.py:255
msgid "手动安装 Proxy"
msgstr "Manually install Proxy"

#: apps/node_man/constants.py:294
msgid "更新"
msgstr "Update"

#: apps/node_man/constants.py:295
msgid "移除"
msgstr "Remove"

#: apps/node_man/constants.py:297
msgid "导入"
msgstr "Import"

#: apps/node_man/constants.py:298 apps/node_man/constants.py:470
msgid "启动"
msgstr "Start"

#: apps/node_man/constants.py:299 apps/node_man/constants.py:471
msgid "停止"
msgstr "Stop"

#: apps/node_man/constants.py:300 apps/node_man/constants.py:473
msgid "重载"
msgstr "Reload"

#: apps/node_man/constants.py:302 apps/node_man/constants.py:474
msgid "托管"
msgstr "Supervised"

#: apps/node_man/constants.py:303 apps/node_man/constants.py:475
msgid "取消托管"
msgstr "Cancel supervised"

#: apps/node_man/constants.py:304
msgid "手动安装"
msgstr "Manual install"

#: apps/node_man/constants.py:305
msgid "打包"
msgstr "Packaging"

#: apps/node_man/constants.py:306 apps/node_man/models.py:566
#: apps/node_man/models.py:1899
msgid "调试"
msgstr "Debug"

#: apps/node_man/constants.py:307
msgid "卸载并删除"
msgstr "Uninstall and delete"

#: apps/node_man/constants.py:308
msgid "下发"
msgstr "Push"

#: apps/node_man/constants.py:309
msgid "忽略"
msgstr "Ignore"

#: apps/node_man/constants.py:311
msgid "下发配置"
msgstr "Push config"

#: apps/node_man/constants.py:312
msgid "策略管控"
msgstr "Policy control"

#: apps/node_man/constants.py:337
msgid "管控区域查看"
msgstr "BK-Net view"

#: apps/node_man/constants.py:338
msgid "管控区域编辑"
msgstr "BK-Net edit"

#: apps/node_man/constants.py:339
msgid "管控区域删除"
msgstr "BK-Net delete"

#: apps/node_man/constants.py:340
msgid "管控区域创建"
msgstr "BK-Net created"

#: apps/node_man/constants.py:341
msgid "接入点编辑"
msgstr "Access Point Edit"

#: apps/node_man/constants.py:342
msgid "接入点删除"
msgstr "Access point deleted"

#: apps/node_man/constants.py:343
msgid "接入点创建"
msgstr "Access Point Creation"

#: apps/node_man/constants.py:344
msgid "接入点查看"
msgstr "Access Point View"

#: apps/node_man/constants.py:345
msgid "任务配置"
msgstr "Task configuration"

#: apps/node_man/constants.py:346
msgid "任务历史查看"
msgstr "Task history view"

#: apps/node_man/constants.py:347
msgid "agent查询"
msgstr "Agent query"

#: apps/node_man/constants.py:348
msgid "agent操作"
msgstr "Agent operation"

#: apps/node_man/constants.py:349
msgid "proxy操作"
msgstr "Proxy operation"

#: apps/node_man/constants.py:350
msgid "插件实例查询"
msgstr "Plugin instance query"

#: apps/node_man/constants.py:351
msgid "插件实例操作"
msgstr "Plugin instance action"

#: apps/node_man/constants.py:352
msgid "插件包导入"
msgstr "Plugin package import"

#: apps/node_man/constants.py:353
msgid "插件包操作"
msgstr "Plugin package action"

#: apps/node_man/constants.py:354
msgid "策略创建"
msgstr "Policy creation"

#: apps/node_man/constants.py:355
msgid "策略查看"
msgstr "Policy view"

#: apps/node_man/constants.py:356
msgid "策略编辑"
msgstr "Policy edit"

#: apps/node_man/constants.py:357
msgid "策略目标选择"
msgstr "Policy target selection"

#: apps/node_man/constants.py:383 apps/node_man/constants.py:722
#: apps/node_man/constants.py:758 apps/node_man/tools/job.py:68
msgid "等待执行"
msgstr "Waiting for execution"

#: apps/node_man/constants.py:387
msgid "部分失败"
msgstr "Partial failure"

#: apps/node_man/constants.py:388
msgid "已终止"
msgstr "Terminated"

#: apps/node_man/constants.py:389
msgid "已移除"
msgstr "Removed"

#: apps/node_man/constants.py:390
msgid "被过滤的"
msgstr "Filtered"

#: apps/node_man/constants.py:391
msgid "已忽略"
msgstr "Ignored"

#: apps/node_man/constants.py:411 apps/node_man/constants.py:424
msgid "未知"
msgstr "Unknown"

#: apps/node_man/constants.py:411 apps/node_man/constants.py:427
#: apps/node_man/handlers/meta.py:326
msgid "正常"
msgstr "Running"

#: apps/node_man/constants.py:411 apps/node_man/constants.py:428
#: apps/node_man/handlers/meta.py:327
msgid "异常"
msgstr "Terminal"

#: apps/node_man/constants.py:411 apps/node_man/constants.py:425
msgid "未安装"
msgstr "Uninstall"

#: apps/node_man/constants.py:426 apps/node_man/constants.py:873
#: apps/node_man/handlers/meta.py:328
msgid "未注册"
msgstr "Unregistered"

#: apps/node_man/constants.py:429
msgid "手动停止"
msgstr "Manual stop"

#: apps/node_man/constants.py:430
msgid "成功"
msgstr "Success"

#: apps/node_man/constants.py:431
msgid "失败"
msgstr "Failed"

#: apps/node_man/constants.py:432
msgid "队列中"
msgstr "In queue"

#: apps/node_man/constants.py:462 apps/node_man/constants.py:467
msgid "官方插件"
msgstr "Official plugin"

#: apps/node_man/constants.py:463 apps/node_man/constants.py:467
msgid "第三方插件"
msgstr "Third-party plugin"

#: apps/node_man/constants.py:464 apps/node_man/constants.py:467
msgid "脚本插件"
msgstr "Script plugin"

#: apps/node_man/constants.py:510 apps/node_man/constants.py:514
msgid "整包部署"
msgstr "Full package deployment"

#: apps/node_man/constants.py:511 apps/node_man/constants.py:514
msgid "功能部署"
msgstr "Feature deployment"

#: apps/node_man/constants.py:512 apps/node_man/constants.py:514
msgid "Agent自动部署"
msgstr "Agent auto-deployment"

#: apps/node_man/constants.py:520
msgid "发布（上线）插件包"
msgstr "Publish (release) plugin package"

#: apps/node_man/constants.py:521
msgid "下线插件包"
msgstr "Offline plugin package"

#: apps/node_man/constants.py:522
msgid "启用插件包"
msgstr "Enable plugin package"

#: apps/node_man/constants.py:523
msgid "停用插件包"
msgstr "Deactivate plugin package"

#: apps/node_man/constants.py:529
msgid "启用插件"
msgstr "Enable plugin"

#: apps/node_man/constants.py:529
msgid "停用插件"
msgstr "Deactivate plugin"

#: apps/node_man/constants.py:718 apps/node_man/constants.py:755
msgid "作业平台API返回中不存在此IP的日志，请联系管理员排查问题"
msgstr ""
"The log of this IP does not exist in the API return of the job platform, "
"please contact the administrator to troubleshoot the problem"

#: apps/node_man/constants.py:719 apps/node_man/constants.py:733
#: apps/node_man/constants.py:737 apps/node_man/constants.py:756
msgid "Agent异常"
msgstr "Agent exception"

#: apps/node_man/constants.py:720
msgid "上次已成功"
msgstr "Last time was successful"

#: apps/node_man/constants.py:725 apps/node_man/models.py:1520
msgid "任务失败"
msgstr "Task failed"

#: apps/node_man/constants.py:726 apps/node_man/constants.py:762
msgid "任务下发失败"
msgstr "Task delivery failed"

#: apps/node_man/constants.py:727
msgid "任务超时"
msgstr "Task timed out"

#: apps/node_man/constants.py:728
msgid "任务日志错误"
msgstr "Task log error"

#: apps/node_man/constants.py:729
msgid "脚本执行失败"
msgstr "Script execution failed"

#: apps/node_man/constants.py:730
msgid "脚本执行超时"
msgstr "Script execution timed out"

#: apps/node_man/constants.py:731
msgid "脚本执行被终止"
msgstr "Script execution terminated"

#: apps/node_man/constants.py:732
msgid "脚本返回码非零"
msgstr "Script return code is non-zero"

#: apps/node_man/constants.py:735
msgid "源文件不存在"
msgstr "Source file does not exist"

#: apps/node_man/constants.py:736
msgid "文件任务超时"
msgstr "File task timed out"

#: apps/node_man/constants.py:738
msgid "用户名不存在"
msgstr "Username does not exist"

#: apps/node_man/constants.py:739
msgid "用户密码错误"
msgstr "Incorrect user password"

#: apps/node_man/constants.py:740
msgid "文件获取失败"
msgstr "Failed to get file"

#: apps/node_man/constants.py:741
msgid "文件超出限制"
msgstr "File exceeds limit"

#: apps/node_man/constants.py:742
msgid "文件传输错误"
msgstr "File transfer error"

#: apps/node_man/constants.py:743
msgid "任务执行出错"
msgstr "Error executing task"

#: apps/node_man/constants.py:744
msgid "任务被强制终止"
msgstr "Task force terminated"

#: apps/node_man/constants.py:763
msgid "任务强制终止成功"
msgstr "Task force terminated successfully"

#: apps/node_man/constants.py:764
msgid "任务强制终止失败"
msgstr "Task force termination failed"

#: apps/node_man/constants.py:776
msgid "是，参数将会在执行详情页面上隐藏"
msgstr "Yes, the parameter will be hidden on the execution details page"

#: apps/node_man/constants.py:776
msgid "否，默认值"
msgstr "No, default"

#: apps/node_man/constants.py:787
msgid "业务集"
msgstr "Business set"

#: apps/node_man/constants.py:802
msgid "shell"
msgstr "Shell"

#: apps/node_man/constants.py:803
msgid "bat"
msgstr "Bat"

#: apps/node_man/constants.py:804
msgid "perl"
msgstr "Perl"

#: apps/node_man/constants.py:805
msgid "python"
msgstr "Python"

#: apps/node_man/constants.py:806
msgid "power_shell"
msgstr "power_shell"

#: apps/node_man/constants.py:829
msgid "启动进程"
msgstr "Start process"

#: apps/node_man/constants.py:830
msgid "停止进程"
msgstr "Stop process"

#: apps/node_man/constants.py:831
msgid "查询进程"
msgstr "Query process"

#: apps/node_man/constants.py:832
msgid "托管进程"
msgstr "Supervised process"

#: apps/node_man/constants.py:833
msgid "取消托管进程"
msgstr "Cancel supervised process"

#: apps/node_man/constants.py:834
msgid "重启进程"
msgstr "Restart process"

#: apps/node_man/constants.py:835
msgid "重载进程"
msgstr "Reload process"

#: apps/node_man/constants.py:853
msgid "未找到"
msgstr "Not Found"

#: apps/node_man/constants.py:854
msgid "查询失败"
msgstr "Query failed"

#: apps/node_man/constants.py:855
msgid "初始状态"
msgstr "Initial state"

#: apps/node_man/constants.py:856
msgid "启动中"
msgstr "Starting"

#: apps/node_man/constants.py:857 apps/node_man/constants.py:874
msgid "运行中"
msgstr "Task in progress"

#: apps/node_man/constants.py:858
msgid "有损状态"
msgstr "Damaged state"

#: apps/node_man/constants.py:859
msgid "繁忙状态"
msgstr "Busy state"

#: apps/node_man/constants.py:860 apps/node_man/constants.py:875
msgid "已停止"
msgstr "Stopped"

#: apps/node_man/constants.py:861
msgid "升级中"
msgstr "Upgrading"

#: apps/node_man/constants.py:888
msgid "已托管"
msgstr "Autoed"

#: apps/node_man/constants.py:889
msgid "未托管"
msgstr "Not auto"

#: apps/node_man/constants.py:912
msgid "2.0 Proxy Agent 安装包代号"
msgstr "2.0 Proxy agent Installation package code"

#: apps/node_man/constants.py:912
msgid "2.0 Agent 安装包代号"
msgstr "2.0 Agent Installation package code"

#: apps/node_man/constants.py:923
msgid "2.0 Proxy 打包根路径"
msgstr "2.0 Proxy package root path"

#: apps/node_man/constants.py:923
msgid "2.0 Agent 打包根路径"
msgstr "2.0 Agent package root path"

#: apps/node_man/constants.py:941
msgid "证书 CA 内容配置"
msgstr "Certificate CA content configuration"

#: apps/node_man/constants.py:942
msgid "Server 侧 CERT 内容配置"
msgstr "Server side CERT content configuration"

#: apps/node_man/constants.py:943
msgid "Server 侧 KEY 内容配置"
msgstr "Server side KEY content configuration"

#: apps/node_man/constants.py:944
msgid "证书密码文件内容配置, 用于解密证书密码"
msgstr ""
"Certificate password file content configuration, used to decrypt the "
"certificate password"

#: apps/node_man/constants.py:945
msgid "API 侧 CERT 内容配置, 用于其他服务调用 GSE"
msgstr "API side CERT content configuration, for other services to call GSE"

#: apps/node_man/constants.py:946
msgid "API 侧 KEY 内容配置, 用于其他服务调用 GSE"
msgstr "API side KEY content configuration, for other services to call GSE"

#: apps/node_man/constants.py:947
msgid "Agent 侧 CERT 内容配置, 用于 Agent 链路"
msgstr "Agent side CERT content configuration, for the Agent link"

#: apps/node_man/constants.py:948
msgid "Agent 侧 KEY 内容配置, 用于 Agent 链路"
msgstr "Agent side KEY content configuration, for the Agent link"

#: apps/node_man/constants.py:962
msgid "周期执行进程"
msgstr "Periodic execution process"

#: apps/node_man/constants.py:963
msgid "常驻进程"
msgstr "Resident process"

#: apps/node_man/constants.py:964
msgid "单次执行进程"
msgstr "Single-execution process"

#: apps/node_man/constants.py:983 apps/node_man/models.py:1876
msgid "主机"
msgstr "Host"

#: apps/node_man/constants.py:983
msgid "自定义"
msgstr "Custom"

#: apps/node_man/constants.py:994
msgid "静态"
msgstr "Static"

#: apps/node_man/constants.py:994
msgid "动态"
msgstr "Dynamic"

#: apps/node_man/constants.py:1005
msgid "IPv4"
msgstr "IPv4"

#: apps/node_man/constants.py:1005
msgid "IPv6"
msgstr "IPv6"

#: apps/node_man/constants.py:1026
msgid "检测 BT 分发策略（下发Py36包）"
msgstr "Detect BT distribution strategy (distribute Py36 package)"

#: apps/node_man/constants.py:1040
msgid "下发安装工具"
msgstr "Transfer installation tool"

#: apps/node_man/constants.py: 1134
msgid "下发windows手动安装工具"
msgstr "Transfer windows manual installation tool"

#: apps/node_man/constants.py:1065
msgid "数据传输工具, 用于下载文件依赖"
msgstr "Data transmission tools for download file dependencies"

#: apps/node_man/constants.py:1066
msgid "用户赋权工具"
msgstr "User Empowerment Tool"

#: apps/node_man/constants.py:1068
msgid "libcurl 共享库, 补丁文件"
msgstr "Libcurl sharing library, patch file"

#: apps/node_man/constants.py:1069
msgid "UNIX 时间工具包"
msgstr "Unix Time Tool Pack"

#: apps/node_man/constants.py:1070
msgid "命令行 JSON 处理器"
msgstr "Command line JSON processor"

#: apps/node_man/constants.py:1071
msgid "base64 工具包"
msgstr "Base64 tools package"

#: apps/node_man/constants.py:1081
msgid "Windows 批处理脚本"
msgstr "Windows batch script"

#: apps/node_man/constants.py:1081
msgid "bash"
msgstr "Bash"

#: apps/node_man/constants.py:1090
msgid "依赖文件"
msgstr "Dependent file"

#: apps/node_man/constants.py:1090
msgid "命令"
msgstr "Command"

#: apps/node_man/exceptions.py:21
msgid "管控区域不存在"
msgstr "BK-Net does not exist"

#: apps/node_man/exceptions.py:26
msgid "管控区域业务已装Agent, 不可删除"
msgstr ""
"Agent has been installed in the \"BK-Net\" business and cannot be deleted"

#: apps/node_man/exceptions.py:31
msgid "该「管控区域」已经存在主机, 不可删除"
msgstr "The host already exists in this \"BK-Net\" and cannot be deleted"

#: apps/node_man/exceptions.py:36
msgid "PAGENT只能安装在「非直连区域」"
msgstr "PAGENT can only be installed in \"Indirect Mode\""

#: apps/node_man/exceptions.py:41
msgid "AGENT只能安装在「直连区域」"
msgstr "AGENT can only be installed in \"Direct Mode\""

#: apps/node_man/exceptions.py:46
msgid "直连区域不可安装Proxy"
msgstr "A proxy cannot be installed in a \"Direct Mode\""

#: apps/node_man/exceptions.py:51
msgid "该接入点不存在."
msgstr "The access point does not exist."

#: apps/node_man/exceptions.py:52
#, python-brace-format
msgid "该接入点[{ap_id}]不存在"
msgstr "The access point [{ap_id}] does not exist"

#: apps/node_man/exceptions.py:57
msgid "该Ip已被占用."
msgstr "This IP is already occupied."

#: apps/node_man/exceptions.py:62
msgid "不存在某Host ID."
msgstr "A Host ID does not exist."

#: apps/node_man/exceptions.py:67
msgid "认证信息校验不通过"
msgstr "Authentication information verification failed"

#: apps/node_man/exceptions.py:77
msgid "没有可用的Proxy"
msgstr "No Proxy available"

#: apps/node_man/exceptions.py:82
msgid "不存在某「管控区域」的权限."
msgstr "There is no permission for a \"BK-Net\"."

#: apps/node_man/exceptions.py:87
msgid "该主机未选择接入点，请尝试重装主机自动选择接入点"
msgstr ""
"The host did not select an access point, please try reinstalling the host to "
"automatically select an access point"

#: apps/node_man/exceptions.py:92
msgid "您不是超级用户无法访问此页面."
msgstr "You are not a superuser and cannot access this page."

#: apps/node_man/exceptions.py:97
msgid "所有ip均被过滤"
msgstr "All ips are filtered"

#: apps/node_man/exceptions.py:102
msgid "该接入点正在被使用"
msgstr "This access point is in use"

#: apps/node_man/exceptions.py:107
msgid "该IP正在运行其他任务"
msgstr "This IP is running another task"

#: apps/node_man/exceptions.py:112
msgid "该主机没有操作系统参数"
msgstr "The host has no operating system parameters"

#: apps/node_man/exceptions.py:117
msgid "您没有该主机的权限"
msgstr "You do not have permission to this host"

#: apps/node_man/exceptions.py:122
msgid "主机不存在"
msgstr "Host does not exist"

#: apps/node_man/exceptions.py:127
msgid "不允许混合操作【手动安装】和【自动安装】的主机"
msgstr ""
"Mixing [manually installed] and [automatically installed] hosts is not "
"allowed"

#: apps/node_man/exceptions.py:132
msgid "配置Proxy策略失败"
msgstr "Failed to configure proxy policy"

#: apps/node_man/exceptions.py:137
msgid "权限中心请求失败"
msgstr "IAM request failed"

#: apps/node_man/exceptions.py:143
msgid "获取全局配置失败"
msgstr "Failed to get global configuration"

#: apps/node_man/exceptions.py:148
msgid "接入点名称重复"
msgstr "Duplicate access point name"

#: apps/node_man/exceptions.py:152
msgid "插件不存在"
msgstr "Plugin does not exist"

#: apps/node_man/exceptions.py:157
msgid "缓存过期，正在重新加载..."
msgstr "Cache expired, reloading..."

#: apps/node_man/exceptions.py:162
msgid "策略不存在"
msgstr "Policy does not exist"

#: apps/node_man/exceptions.py:167
msgid "插件配置模板不存在"
msgstr "Plugin configuration template does not exist"

#: apps/node_man/exceptions.py:172
msgid "创建插件记录失败"
msgstr "Failed to create plugin record"

#: apps/node_man/exceptions.py:177
msgid "接入点不支持此机器的操作系统."
msgstr "The access point does not support this machine's operating system."

#: apps/node_man/exceptions.py:178
#, python-brace-format
msgid "接入点[{ap_id}]不支持此机器的操作系统[{os_type}]"
msgstr "Access point [{ap_id}] does not support this machine's OS [{os_type}]"

#: apps/node_man/exceptions.py:183
#, python-brace-format
msgid "策略 -> {policy_id}「{name}」正在执行"
msgstr "Policy->{policy_id}{name} is running"

#: apps/node_man/exceptions.py:189
msgid "主机的安装通道不存在，请重新选择"
msgstr ""
"The installation channel for the host does not exist, please select again"

#: apps/node_man/exceptions.py:193
msgid "插件上传失败"
msgstr "Plugin upload failed"

#: apps/node_man/exceptions.py:194
#, python-brace-format
msgid "插件上传失败: plugin_name -> {plugin_name}, error -> {error}"
msgstr ""
"Failed to upload plugin: plugin_name -> {plugin_name}, error -> {error}"

#: apps/node_man/exceptions.py:199
msgid "访问地址不可达"
msgstr "Access address unreachable"

#: apps/node_man/exceptions.py:204
msgid "插件配置未变更，无需执行"
msgstr "Plugin configuration has not changed, no need to execute"

#: apps/node_man/exceptions.py:209
msgid "远程采集主机不存在"
msgstr "Remote collect host does not exist"

#: apps/node_man/exceptions.py:214
msgid "服务实例不存在"
msgstr "Subscription task instance does not exist"

#: apps/node_man/exceptions.py:215
#, python-brace-format
msgid "服务实例 -> [{id}] 不存在"
msgstr "Service instance -> [{id}] does not exist"

#: apps/node_man/handlers/cloud.py:46
#, python-brace-format
msgid "不存在ID为: {bk_cloud_id} 的「管控区域」"
msgstr "No \"BK-Net\" with id: {bk_cloud_id}"

#: apps/node_man/handlers/cloud.py:50 apps/node_man/handlers/cloud.py:135
msgid "自动选择接入点"
msgstr "Auto select access point"

#: apps/node_man/handlers/cloud.py:205
msgid "管控区域不可名为「直连区域」"
msgstr "The \"BK-Net\" cannot be named as \"Direct Mode\""

#: apps/node_man/handlers/cloud.py:209 apps/node_man/handlers/cloud.py:237
msgid "管控区域名称不可重复"
msgstr "BK-Net name cannot be repeated"

#: apps/node_man/handlers/cloud.py:226 apps/node_man/handlers/policy.py:579
#: apps/node_man/views/ap.py:266
msgid "权限中心创建关联权限失败: {}"
msgstr "Permission Center failed to create associated permissions: {}"

#: apps/node_man/handlers/cloud.py:255
msgid "该区域已存在主机"
msgstr "Host already exists in this area"

#: apps/node_man/handlers/cmdb.py:336
msgid "在CMDB中，还有主机关联到当前「管控区域」下，无法删除"
msgstr ""
"In the CMDB, there are hosts associated with the current \"BK-Net\" and "
"cannot be deleted"

#: apps/node_man/handlers/healthz/saas_healthz.py:110
#, python-format
msgid "第三方组件%s异常"
msgstr "The third-party component %s is abnormal"

#: apps/node_man/handlers/healthz/saas_healthz.py:110
msgid "请检查esb及对应组件服务是否正常"
msgstr ""
"Please check whether the esb and corresponding component services are normal"

#: apps/node_man/handlers/healthz/saas_healthz.py:114
#: apps/node_man/serializers/job.py:68 pipeline/engine/models/core.py:876
msgid "状态"
msgstr "Status"

#: apps/node_man/handlers/healthz/saas_healthz.py:177
#: apps/node_man/handlers/healthz/saas_healthz.py:248
#: apps/node_man/handlers/healthz/saas_healthz.py:319
msgid "{} 不存在对应的测试用例"
msgstr "{} does not have a corresponding test case"

#: apps/node_man/handlers/healthz/saas_healthz.py:201
#: apps/node_man/handlers/healthz/saas_healthz.py:272
#: apps/node_man/handlers/healthz/saas_healthz.py:343
#, python-brace-format
msgid "cc中不存在{method_name}"
msgstr "{method_name} does not exist in cc"

#: apps/node_man/handlers/healthz/saas_healthz.py:219
#: apps/node_man/handlers/healthz/saas_healthz.py:290
#: apps/node_man/handlers/healthz/saas_healthz.py:368
#: apps/node_man/handlers/healthz/saas_healthz.py:490
msgid "接口不具有依赖关系"
msgstr "Interface has no dependencies"

#: apps/node_man/handlers/healthz/saas_healthz.py:222
#: apps/node_man/handlers/healthz/saas_healthz.py:293
#: apps/node_man/handlers/healthz/saas_healthz.py:371
msgid "cc中没有此接口"
msgstr "No such interface in cc"

#: apps/node_man/handlers/healthz/saas_healthz.py:414
msgid "job不存在对应的测试用例"
msgstr "Job does not have a corresponding test case"

#: apps/node_man/handlers/healthz/saas_healthz.py:417
msgid "job 中不存在接口 fast_execute_script 的相关参数"
msgstr ""
"The related parameters of the interface fast_execute_script do not exist in "
"the job"

#: apps/node_man/handlers/healthz/saas_healthz.py:458
#, python-brace-format
msgid "job中不存在{method_name}"
msgstr "{method_name} does not exist in job"

#: apps/node_man/handlers/healthz/saas_healthz.py:469
#, python-format
msgid "查找业务下主机失败,主机查询结果:%s"
msgstr "Failed to find the host under the service, host query result: %s"

#: apps/node_man/handlers/healthz/saas_healthz.py:493
msgid "job中没有此接口"
msgstr "No such interface in job"

#: apps/node_man/handlers/host.py:414 apps/node_man/handlers/permission.py:413
#, python-brace-format
msgid "Host ID:{bk_host_id} 不存在"
msgstr "Host ID: {bk_host_id} does not exist"

#: apps/node_man/handlers/host.py:422
#, python-brace-format
msgid "外网IP：{outer_ip} 已被占用"
msgstr "External IP: {outer_ip} is occupied"

#: apps/node_man/handlers/host.py:427
#, python-brace-format
msgid "登录IP：{login_ip} 已被占用"
msgstr "Login in IP: {login_ip} is occupied"

#: apps/node_man/handlers/host.py:435
#, python-brace-format
msgid "Ap_id:{ap_id} 不存在于数据库."
msgstr "Ap_id:{ap_id} does not exist in the database."

#: apps/node_man/handlers/job.py:48 apps/node_man/handlers/permission.py:393
#, python-brace-format
msgid "不存在ID为{job_id}的任务"
msgstr "No job with id {job_id} exists"

#: apps/node_man/handlers/job.py:742
msgid "没有需要变更的实例"
msgstr "No instance to change"

#: apps/node_man/handlers/meta.py:155
msgid "手动"
msgstr "Manual"

#: apps/node_man/handlers/meta.py:155
msgid "远程"
msgstr "Remote"

#: apps/node_man/handlers/meta.py:171 apps/node_man/handlers/meta.py:294
#: apps/node_man/handlers/meta.py:365 apps/node_man/models.py:363
#: apps/node_man/models.py:1687 apps/node_man/serializers/job.py:104
msgid "操作系统"
msgstr "Operating system"

#: apps/node_man/handlers/meta.py:171
msgid "其它"
msgstr "Other"

#: apps/node_man/handlers/meta.py:172 apps/node_man/handlers/meta.py:308
#: apps/node_man/handlers/meta.py:378
msgid "Agent状态"
msgstr "Agent status"

#: apps/node_man/handlers/meta.py:173
msgid "安装方式"
msgstr "Install method"

#: apps/node_man/handlers/meta.py:174 apps/node_man/handlers/meta.py:301
#: apps/node_man/handlers/meta.py:430 apps/node_man/serializers/host.py:22
#: apps/node_man/serializers/host.py:72 apps/node_man/serializers/job.py:278
#: apps/node_man/serializers/plugin.py:197
#: apps/node_man/serializers/plugin.py:228
msgid "Agent版本"
msgstr "Agent version"

#: apps/node_man/handlers/meta.py:175 apps/node_man/handlers/meta.py:284
#: apps/node_man/handlers/meta.py:363
msgid "管控区域"
msgstr "BK-Net"

#: apps/node_man/handlers/meta.py:176 apps/node_man/handlers/meta.py:357
#: apps/node_man/models.py:352 apps/node_man/serializers/job.py:90
msgid "寻址方式"
msgstr "Addressing mode"

#: apps/node_man/handlers/meta.py:177 apps/node_man/models.py:373
msgid "安装通道"
msgstr "Install channel"

#: apps/node_man/handlers/meta.py:179
msgid "IP"
msgstr "IP"

#: apps/node_man/handlers/meta.py:257 apps/node_man/serializers/job.py:69
#: pipeline/models.py:618
msgid "执行者"
msgstr "Executor"

#: apps/node_man/handlers/meta.py:258
msgid "执行状态"
msgstr "Task status"

#: apps/node_man/handlers/meta.py:259 apps/node_man/serializers/job.py:182
#: apps/node_man/serializers/job.py:276 apps/node_man/serializers/plugin.py:225
#: apps/node_man/serializers/plugin_v2.py:193
#: apps/node_man/serializers/policy.py:152
msgid "任务类型"
msgstr "Task type"

#: apps/node_man/handlers/meta.py:260 apps/node_man/serializers/iam.py:19
#: apps/node_man/serializers/job.py:190 apps/node_man/serializers/policy.py:168
msgid "操作类型"
msgstr "Operation type"

#: apps/node_man/handlers/meta.py:261
msgid "策略名称"
msgstr "Policy name"

#: apps/node_man/handlers/meta.py:322 apps/node_man/handlers/meta.py:445
msgid "无版本"
msgstr "No version"

#: apps/node_man/handlers/meta.py:324 apps/node_man/handlers/meta.py:451
msgid "{}状态"
msgstr "{}status"

#: apps/node_man/handlers/meta.py:389 apps/node_man/models.py:369
#: apps/node_man/models.py:1909 apps/node_man/serializers/job.py:191
#: apps/node_man/serializers/job.py:287 apps/node_man/serializers/plugin.py:239
msgid "节点类型"
msgstr "Node type"

#: apps/node_man/handlers/meta.py:443
#, python-brace-format
msgid "{name}版本"
msgstr "{name} version"

#: apps/node_man/handlers/permission.py:39
#: apps/node_man/handlers/permission.py:90
msgid "您没有权限执行操作"
msgstr "You do not have permission to perform the operation"

#: apps/node_man/handlers/permission.py:53
msgid "您没有该接入点的权限"
msgstr "You do not have permission to this access point"

#: apps/node_man/handlers/permission.py:75
msgid "您没有创建接入点的权限"
msgstr "You do not have permission to create an access point"

#: apps/node_man/handlers/permission.py:76
msgid "您没有编辑该接入点的权限"
msgstr "You do not have permission to edit this access point"

#: apps/node_man/handlers/permission.py:77
msgid "您没有删除该接入点的权限"
msgstr "You do not have permission to delete this access point"

#: apps/node_man/handlers/permission.py:78
msgid "您没有查看该接入点详情的权限"
msgstr "You do not have permission to view details of this access point"

#: apps/node_man/handlers/permission.py:111
#, fuzzy, python-brace-format
msgid "不存在ID为: {bk_cloud_id} 的管控区域"
msgstr "No \"BK-Net\" with id: {bk_cloud_id}"

#: apps/node_man/handlers/permission.py:143
msgid "您没有创建「管控区域」的权限"
msgstr "You do not have permission to create \"BK-Net\""

#: apps/node_man/handlers/permission.py:144
msgid "您没有编辑该「管控区域」的权限"
msgstr "You do not have permission to edit this \"BK-Net\""

#: apps/node_man/handlers/permission.py:145
msgid "您没有删除该「管控区域」的权限"
msgstr "You do not have permission to delete this \"BK-Net\""

#: apps/node_man/handlers/permission.py:146
msgid "您没有查看该「管控区域」详情的权限"
msgstr "You do not have permission to view details for this \"BK-Net\""

#: apps/node_man/handlers/permission.py:158
msgid "您没有权限执行操作，请申请「管控区域」编辑权限"
msgstr ""
"You do not have permission to perform the operation, please apply for \"BK-"
"Net\" editing permission"

#: apps/node_man/handlers/permission.py:184
msgid "您没有查询Debug接口的权限"
msgstr "You do not have permission to query the Debug interface"

#: apps/node_man/handlers/permission.py:326
msgid "【资源池】业务暂不支持从权限中心申请，请在admin中将用户添加为superuser"
msgstr ""
"[Resource pool] service does not currently support application from the "
"permission center, please add the user as superuser in admin"

#: apps/node_man/handlers/plugin_v2.py:136
#, python-brace-format
msgid "插件配置模板{ids}不存在或已下线"
msgstr "Plugin configuration template {ids} does not exist or is offline"

#: apps/node_man/handlers/policy.py:86 apps/node_man/serializers/policy.py:175
#: apps/node_man/tools/policy.py:36
#, python-brace-format
msgid "不存在ID为: {id} 的策略"
msgstr "Policy with id: {id} does not exist"

#: apps/node_man/handlers/policy.py:703
#, python-brace-format
msgid ""
"{plugin_name}: {plugin_description} - V{version} \n"
" 该版本支持{plugin_scenario}"
msgstr ""
"{plugin_name}: {plugin_description} - V{version} \n"
"This version supports {plugin_scenario}"

#: apps/node_man/handlers/validator.py:240
msgid "修改了认证类型为秘钥认证，但是没有输入认证凭证"
msgstr ""
"Modified authentication type to key authentication, but no authentication "
"credentials have been entered"

#: apps/node_man/handlers/validator.py:244
msgid "修改了认证类型为密码认证，但是没有输入密码"
msgstr ""
"Modified authentication type to password authentication, but no password was "
"entered"

#: apps/node_man/handlers/validator.py:257
msgid "认证信息已过期，请输入相关认证信息"
msgstr ""
"The authentication information has expired, please enter the relevant "
"authentication information"

#: apps/node_man/handlers/validator.py:328
#, python-brace-format
msgid ""
"已有 Agent 存活的动态寻址主机【bk_host_id: {bk_host_id}】位于所选「管控区"
"域」：{bk_cloud_name}，业务：{bk_biz_name}"
msgstr ""
"The dynamic addressing host of Agent [bk_host_id: {bk_host_id}] is located "
"in the selected \"BK-Net\": {bk_cloud_name}, business: {bk_biz_name}"

#: apps/node_man/handlers/validator.py:346
#, python-brace-format
msgid ""
"\n"
"            该主机内网IP已存在于所选「管控区域」：{bk_cloud_name} 下,\n"
"            业务：{bk_biz_name},\n"
"            节点类型：{node_type}\n"
"            "
msgstr ""
"\n"
"                The host's intranet IP already exists in the selected \"BK-"
"Net\": {bk_cloud_name},\n"
"                Business: {bk_biz_name},\n"
"                Node type: {node_type}\n"
"                "

#: apps/node_man/handlers/validator.py:363
#, python-brace-format
msgid ""
"该主机(bk_host_id:{bk_host_id}) 已存在且 IP 信息为：IPv4({ipv4}), "
"IPv6({ipv6})，不允许修改为 IPv4({ipv4}), IPv6({to_be_add_ipv6})"
msgstr ""
"The host (bk_host_id:{bk_host_id}) already exists and the IP information is: "
"IPv4({ipv4}), IPv6({ipv6}), it is not allowed to change to IPv4({ipv4}), "
"IPv6({to_be_add_ipv6})"

#: apps/node_man/handlers/validator.py:390
#, python-brace-format
msgid "尚未被安装，无法执行 {op_type} 操作"
msgstr "Not installed, cannot perform {op_type} operation"

#: apps/node_man/handlers/validator.py:400
#, python-brace-format
msgid "Host ID 不正确，无法执行 {op_type} 操作"
msgstr "Incorrect Host ID for {op_type} operation"

#: apps/node_man/handlers/validator.py:408
#, python-brace-format
msgid ""
"节点类型不正确，该主机是 {host_node_type}, 而请求的操作类型是 {node_type}"
msgstr ""
"Incorrect node type, the host is {host_node_type}, and the requested "
"operation type is {node_type}"

#: apps/node_man/handlers/validator.py:506
#, python-brace-format
msgid "主机(IP:{ip}) 没有操作系统, 请【重装】并补全相关信息"
msgstr ""
"The host (IP:{ip}) has no operating system, please [reinstall] and complete "
"the relevant information"

#: apps/node_man/handlers/validator.py:510
#, python-brace-format
msgid "管控区域(ID:{bk_cloud_id}) 不存在"
msgstr "The BK-Net (ID:{bk_cloud_id}) does not exist"

#: apps/node_man/handlers/validator.py:515
#, python-brace-format
msgid "{bk_cloud_name} 是「直连区域」，不可以安装PROXY"
msgstr "{bk_cloud_name} is \"Direct Mode\", PROXY cannot be installed"

#: apps/node_man/handlers/validator.py:525
msgid "该「管控区域」下不存在代理"
msgstr "No Proxy exists in this \"BK-Net\""

#: apps/node_man/handlers/validator.py:533
msgid "直连区域必须选择接入点"
msgstr "Access point must be selected for \"Direct Mode\""

#: apps/node_man/handlers/validator.py:537
#, python-brace-format
msgid "接入点(id:{ap_id})不存在"
msgstr "Access point (id: {ap_id}) does not exist"

#: apps/node_man/handlers/validator.py:586
#, python-brace-format
msgid "主机(IP:{inner_ip}) 没有操作系统, 请【重装】并补全相关信息"
msgstr ""
"The host (IP:{inner_ip}) has no operating system, please [reinstall] and "
"complete the relevant information"

#: apps/node_man/handlers/validator.py:590
msgid "不允许操作【管控区域不存在】的主机,「管控区域」id: {}"
msgstr ""
"Not allowed to operate the host of [BK-Net does not exist], \"BK-Net\" id: {}"

#: apps/node_man/models.py:134
msgid "键"
msgstr "Key"

#: apps/node_man/models.py:135
msgid "值"
msgstr "Value"

#: apps/node_man/models.py:171 apps/node_man/models.py:172
msgid "配置表（GlobalSettings）"
msgstr "Configuration Table (GlobalSettings)"

#: apps/node_man/models.py:190
#, python-brace-format
msgid "后台获取密钥失败：{e}"
msgstr "Failed to get key in background: {e}"

#: apps/node_man/models.py:201
#, python-brace-format
msgid "SaaS获取密钥失败：{e}"
msgstr "SaaS failed to get key: {e}"

#: apps/node_man/models.py:323 apps/node_man/models.py:570
#: apps/node_man/models.py:954 apps/node_man/serializers/ap.py:81
#: apps/node_man/serializers/base.py:101 apps/node_man/serializers/debug.py:25
#: apps/node_man/serializers/host.py:36 apps/node_man/serializers/host.py:69
#: apps/node_man/serializers/job.py:88 apps/node_man/serializers/job.py:280
#: apps/node_man/serializers/job.py:396 apps/node_man/serializers/plugin.py:234
#: apps/node_man/serializers/policy.py:163
msgid "主机ID"
msgstr "Host ID"

#: apps/node_man/models.py:325 apps/node_man/serializers/host.py:48
#: apps/node_man/serializers/job.py:105
msgid "认证类型"
msgstr "Auth type"

#: apps/node_man/models.py:327
msgid "账户名"
msgstr "Account name"

#: apps/node_man/models.py:328 apps/node_man/models.py:651
#: apps/node_man/serializers/host.py:49 apps/node_man/serializers/job.py:107
msgid "密码"
msgstr "Password"

#: apps/node_man/models.py:329 apps/node_man/serializers/job.py:108
msgid "端口"
msgstr "Port"

#: apps/node_man/models.py:330 apps/node_man/serializers/job.py:109
msgid "密钥"
msgstr "Key"

#: apps/node_man/models.py:331
msgid "额外认证资料"
msgstr "Additional credentials"

#: apps/node_man/models.py:332
msgid "保留天数"
msgstr "Retention days"

#: apps/node_man/models.py:336 apps/node_man/models.py:337
msgid "临时认证数据（IdentityData）"
msgstr "Temporary IdentityData"

#: apps/node_man/models.py:347
msgid "CMDB主机ID"
msgstr "CMDB host ID"

#: apps/node_man/models.py:348
msgid "AgentID"
msgstr "AgentID"

#: apps/node_man/models.py:350 apps/node_man/models.py:873
#: apps/node_man/serializers/host.py:21 apps/node_man/serializers/host.py:28
#: apps/node_man/serializers/host.py:37
#: apps/node_man/serializers/install_channel.py:22
#: apps/node_man/serializers/job.py:87 apps/node_man/serializers/plugin.py:196
#: apps/node_man/serializers/plugin.py:227
msgid "管控区域ID"
msgstr "BK-Net ID"

#: apps/node_man/models.py:355 apps/node_man/serializers/ap.py:79
#: apps/node_man/serializers/host.py:40 apps/node_man/serializers/job.py:98
msgid "外网IP"
msgstr "External IP"

#: apps/node_man/models.py:356 apps/node_man/serializers/host.py:43
#: apps/node_man/serializers/job.py:99
msgid "登录IP"
msgstr "Login IP"

#: apps/node_man/models.py:357 apps/node_man/serializers/host.py:44
#: apps/node_man/serializers/job.py:100
msgid "数据IP"
msgstr "Data IP"

#: apps/node_man/models.py:360 apps/node_man/serializers/ap.py:80
#: apps/node_man/serializers/host.py:41 apps/node_man/serializers/job.py:102
msgid "外网IPv6"
msgstr "External IPv6"

#: apps/node_man/models.py:366 apps/node_man/models.py:1690
msgid "CPU架构"
msgstr "CPU Architecture"

#: apps/node_man/models.py:368
msgid "操作系统版本"
msgstr "Operating system version"

#: apps/node_man/models.py:370
msgid "节点来源"
msgstr "Node source"

#: apps/node_man/models.py:371
msgid "是否手动安装"
msgstr "Is manual"

#: apps/node_man/models.py:374 apps/node_man/models.py:841
#: apps/node_man/serializers/ap.py:34 apps/node_man/serializers/cloud.py:30
#: apps/node_man/serializers/host.py:47 apps/node_man/serializers/job.py:95
msgid "接入点ID"
msgstr "Access Point ID"

#: apps/node_man/models.py:375 apps/node_man/models.py:842
#, fuzzy
msgid "GSE1.0接入点ID"
msgstr "GSE 1.0 Access Point ID"

#: apps/node_man/models.py:376 apps/node_man/models.py:875
#: apps/node_man/serializers/install_channel.py:32
msgid "上游节点"
msgstr "Upstream node"

#: apps/node_man/models.py:381
msgid "额外数据"
msgstr "Extra data"

#: apps/node_man/models.py:404
#, python-brace-format
msgid "bk_host_id={bk_host_id} 主机信息不存在"
msgstr "bk_host_id={bk_host_id} host information does not exist"

#: apps/node_man/models.py:424
#, python-brace-format
msgid "{bk_cloud_id}:{ip} 主机信息不存在"
msgstr "{bk_cloud_id}:{ip} host information does not exist"

#: apps/node_man/models.py:440
msgid "主机所属「管控区域」不存在可用Proxy"
msgstr ""
"There is no available proxy in the \"BK-Net\" to which the host belongs"

#: apps/node_man/models.py:490
#, python-brace-format
msgid "安装节点主机{inner_ip}不存在，请确认是否已安装AGENT"
msgstr ""
"The installation node host {inner_ip} does not exist, please confirm whether "
"AGENT is installed"

#: apps/node_man/models.py:552 apps/node_man/models.py:553
msgid "主机（Host）"
msgstr "Host"

#: apps/node_man/models.py:564
msgid "默认"
msgstr "Default"

#: apps/node_man/models.py:571
msgid "进程名称"
msgstr "Process name"

#: apps/node_man/models.py:573
msgid "进程状态"
msgstr "Process status"

#: apps/node_man/models.py:580
msgid "是否自动启动"
msgstr "Whether to start automatically"

#: apps/node_man/models.py:582
msgid "进程版本"
msgstr "Process version"

#: apps/node_man/models.py:584
msgid "进程类型"
msgstr "Process type"

#: apps/node_man/models.py:587
msgid "配置文件"
msgstr "Config file"

#: apps/node_man/models.py:588
msgid "监听IP"
msgstr "Listening IP"

#: apps/node_man/models.py:589
msgid "监听端口"
msgstr "Listening port"

#: apps/node_man/models.py:591
msgid "二进制文件所在路径"
msgstr "Path to binary file"

#: apps/node_man/models.py:592 apps/node_man/models.py:1315
msgid "日志路径"
msgstr "Log path"

#: apps/node_man/models.py:593 apps/node_man/models.py:1316
#: apps/node_man/serializers/host.py:53 apps/node_man/serializers/job.py:114
msgid "数据文件路径"
msgstr "Data file path"

#: apps/node_man/models.py:594 apps/node_man/models.py:1317
msgid "pid文件路径"
msgstr "Pid file path"

#: apps/node_man/models.py:596
msgid "插件组ID"
msgstr "Plugin Group ID"

#: apps/node_man/models.py:598
msgid "来源类型"
msgstr "Source type"

#: apps/node_man/models.py:600
msgid "来源ID"
msgstr "Source ID"

#: apps/node_man/models.py:604
msgid "是否是最新记录"
msgstr "Is it the latest record"

#: apps/node_man/models.py:634 apps/node_man/models.py:635
msgid "进程状态（ProcessStatus）"
msgstr "ProcessStatus"

#: apps/node_man/models.py:639 apps/node_man/serializers/ap.py:35
msgid "接入点名称"
msgstr "Access Point Name"

#: apps/node_man/models.py:640 apps/node_man/serializers/ap.py:36
msgid "接入点类型"
msgstr "Access Point Type"

#: apps/node_man/models.py:641 apps/node_man/serializers/ap.py:37
msgid "区域id"
msgstr "Area id"

#: apps/node_man/models.py:642 apps/node_man/serializers/ap.py:38
msgid "城市id"
msgstr "City id"

#: apps/node_man/models.py:644
msgid "GSE 版本"
msgstr "GSE Version"

#: apps/node_man/models.py:646 apps/node_man/serializers/ap.py:39
msgid "GSE BT文件服务器列表"
msgstr "GSE BT file server list"

#: apps/node_man/models.py:647 apps/node_man/serializers/ap.py:40
msgid "GSE 数据服务器列表"
msgstr "GSE Data Server List"

#: apps/node_man/models.py:648 apps/node_man/serializers/ap.py:41
msgid "GSE 任务服务器列表"
msgstr "GSE Task Server List"

#: apps/node_man/models.py:649 apps/node_man/serializers/ap.py:42
msgid "ZK服务器列表"
msgstr "ZK server list"

#: apps/node_man/models.py:650 apps/node_man/serializers/ap.py:43
#: apps/node_man/serializers/ap.py:100
msgid "ZK账号"
msgstr "ZK account"

#: apps/node_man/models.py:652 apps/node_man/serializers/ap.py:44
msgid "安装包内网地址"
msgstr "Installation package intranet address"

#: apps/node_man/models.py:653 apps/node_man/serializers/ap.py:45
msgid "安装包外网地址"
msgstr "Installation package external network address"

#: apps/node_man/models.py:655
msgid "Nginx路径"
msgstr "Nginx path"

#: apps/node_man/models.py:656 apps/node_man/serializers/ap.py:46
msgid "Agent配置信息"
msgstr "Agent configuration information"

#: apps/node_man/models.py:657 apps/node_man/serializers/ap.py:47
msgid "接入点状态"
msgstr "Access Point Status"

#: apps/node_man/models.py:658 apps/node_man/serializers/ap.py:48
#: apps/node_man/serializers/ap.py:103
msgid "接入点描述"
msgstr "Access Point Description"

#: apps/node_man/models.py:659 apps/node_man/serializers/ap.py:49
msgid "是否启用"
msgstr "Enable"

#: apps/node_man/models.py:660 apps/node_man/serializers/ap.py:50
msgid "是否默认接入点，不可删除"
msgstr "Is it the default access point, it cannot be deleted"

#: apps/node_man/models.py:661 apps/node_man/serializers/ap.py:104
msgid "接入点创建者"
msgstr "Access Point Creator"

#: apps/node_man/models.py:662
msgid "GSE端口配置"
msgstr "GSE port configuration"

#: apps/node_man/models.py:663 apps/node_man/serializers/ap.py:51
msgid "Proxy上的安装包"
msgstr "Package on Proxy"

#: apps/node_man/models.py:664 apps/node_man/serializers/ap.py:108
msgid "节点管理外网回调地址"
msgstr "Node management external network callback address"

#: apps/node_man/models.py:665 apps/node_man/serializers/ap.py:109
msgid "节点管理内网回调地址"
msgstr "Node management intranet callback address"

#: apps/node_man/models.py:695
msgid "默认接入点不存在"
msgstr "Default access point does not exist"

#: apps/node_man/models.py:730
#, python-brace-format
msgid "回调地址请求失败，报错信息 -> {exc}"
msgstr "Callback address request failed, error message -> {exc}"

#: apps/node_man/models.py:732
msgid "回调地址指向站点非节点管理后台"
msgstr ""
"The callback address points to the site's non-node management background"

#: apps/node_man/models.py:761
#, python-brace-format
msgid "Ping {ip} 失败, {output}"
msgstr "Ping {ip} failed, {output}"

#: apps/node_man/models.py:763
#, python-brace-format
msgid "Ping {ip} 正常"
msgstr "Ping {ip} OK"

#: apps/node_man/models.py:775
#, python-brace-format
msgid "{download_url} 检测下载失败，目标地址没有 setup_agent.sh 文件"
msgstr ""
"{download_url} failed to detect download, the target address does not have a "
"setup_agent.sh file"

#: apps/node_man/models.py:783
#, python-brace-format
msgid "{download_url} 检测下载失败"
msgstr "{download_url} failed to detect download"

#: apps/node_man/models.py:790
#, python-brace-format
msgid "{download_url} 检测下载成功"
msgstr "{download_url} detected successful download"

#: apps/node_man/models.py:798
#, python-brace-format
msgid "回调地址 -> {url} 验证可达"
msgstr "Callback address->{url} is verified to be reachable"

#: apps/node_man/models.py:803
#, python-brace-format
msgid "回调地址 -> {url} 不正确：{err_msg}"
msgstr "Callback address->{url} is incorrect: {err_msg}"

#: apps/node_man/models.py:831 apps/node_man/models.py:832
msgid "接入点（AccessPoint）"
msgstr "AccessPoint"

#: apps/node_man/models.py:840 apps/node_man/serializers/cloud.py:29
msgid "云服务商"
msgstr "Cloud provider"

#: apps/node_man/models.py:843
msgid "管控区域创建者"
msgstr "BK-Net creator"

#: apps/node_man/models.py:845
msgid "是否可见"
msgstr "Is visible"

#: apps/node_man/models.py:846 apps/utils/orm.py:98 pipeline/models.py:284
msgid "是否删除"
msgstr "Delete"

#: apps/node_man/models.py:857 apps/node_man/models.py:858
msgid "管控区域（BK-Net）"
msgstr "BK-Net"

#: apps/node_man/models.py:872
msgid "名称"
msgstr "Name"

#: apps/node_man/models.py:874
msgid "安装通道跳板机"
msgstr "Install channel jumper"

#: apps/node_man/models.py:920 apps/node_man/models.py:921
msgid "安装通道（InstallChannel）"
msgstr "InstallChannel"

#: apps/node_man/models.py:927 apps/node_man/models.py:1125
#: apps/node_man/models.py:1915
msgid "操作人"
msgstr "Operator"

#: apps/node_man/models.py:929
msgid "作业类型"
msgstr "Job type"

#: apps/node_man/models.py:932
msgid "任务ID列表"
msgstr "Task id list"

#: apps/node_man/models.py:933
msgid "创建任务时间"
msgstr "Task created time"

#: apps/node_man/models.py:934
msgid "任务结束时间"
msgstr "Task end time"

#: apps/node_man/models.py:936 apps/node_man/models.py:1534
#: apps/node_man/models.py:2284 apps/node_man/models.py:2433
msgid "任务状态"
msgstr "Task status"

#: apps/node_man/models.py:938
msgid "全局运行参数"
msgstr "Global run parameters"

#: apps/node_man/models.py:939
msgid "任务统计信息"
msgstr "Task statistics"

#: apps/node_man/models.py:940 apps/node_man/models.py:1920
msgid "业务范围"
msgstr "Business Scope"

#: apps/node_man/models.py:941
msgid "发生错误的主机"
msgstr "Bad host"

#: apps/node_man/models.py:942 apps/node_man/models.py:2234
msgid "是否为自动触发"
msgstr "Is it auto-triggered"

#: apps/node_man/models.py:945 apps/node_man/models.py:946
msgid "任务信息（Job）"
msgstr "Job Information (Job)"

#: apps/node_man/models.py:953
msgid "作业ID"
msgstr "Job ID"

#: apps/node_man/models.py:955 apps/node_man/models.py:2273
#: apps/node_man/models.py:2448 apps/node_man/serializers/iam.py:23
#: apps/node_man/serializers/plugin_v2.py:240
#: pipeline/contrib/statistics/models.py:36
#: pipeline/contrib/statistics/models.py:72 pipeline/models.py:611
msgid "实例ID"
msgstr "Instance ID"

#: apps/node_man/models.py:956
msgid "Pipeline节点ID"
msgstr "Pipeline node ID"

#: apps/node_man/models.py:958
msgid "当前步骤"
msgstr "Current step"

#: apps/node_man/models.py:964 apps/node_man/models.py:965
msgid "任务详情（JobTask）"
msgstr "JobTask"

#: apps/node_man/models.py:974 apps/node_man/models.py:1651
#: apps/node_man/models.py:1671
msgid "插件名"
msgstr "Plugin name"

#: apps/node_man/models.py:975
msgid "插件描述"
msgstr "Plugin Description"

#: apps/node_man/models.py:976
msgid "使用场景"
msgstr "Use scenario"

#: apps/node_man/models.py:977
msgid "英文插件描述"
msgstr "English plugin description"

#: apps/node_man/models.py:978
msgid "英文使用场景"
msgstr "English usage scenarios"

#: apps/node_man/models.py:979
msgid "所属范围"
msgstr "Belongs to"

#: apps/node_man/models.py:981
msgid "宿主节点类型要求"
msgstr "Host node type required"

#: apps/node_man/models.py:984
msgid "配置文件名称"
msgstr "Config file name"

#: apps/node_man/models.py:986
msgid "配置文件格式类型"
msgstr "Config file format type"

#: apps/node_man/models.py:994
msgid "是否使用数据库"
msgstr "Whether to use database"

#: apps/node_man/models.py:995
msgid "是否在成功安装agent后自动拉起"
msgstr ""
"Whether to automatically pull up the agent after successful installation of "
"the agent"

#: apps/node_man/models.py:996
msgid "是否二进制文件"
msgstr "Is it binary"

#: apps/node_man/models.py:997
msgid "是否启用插件"
msgstr "Whether plugins are enabled"

#: apps/node_man/models.py:1000
msgid "部署方式"
msgstr "Deployment method"

#: apps/node_man/models.py:1003
msgid "托管类型"
msgstr "Auto type"

#: apps/node_man/models.py:1005
msgid "来源系统APP CODE"
msgstr "Source system APP CODE"

#: apps/node_man/models.py:1007
msgid "节点管理管控插件信息"
msgstr "Node management control plugin information"

#: apps/node_man/models.py:1010 apps/node_man/models.py:1011
msgid "插件信息（GsePluginDesc）"
msgstr "Plugin Info (GsePluginDesc)"

#: apps/node_man/models.py:1105
msgid "压缩包名"
msgstr "Compressed package name"

#: apps/node_man/models.py:1106 apps/node_man/models.py:1672
msgid "版本号"
msgstr "Version"

#: apps/node_man/models.py:1107
msgid "所属服务"
msgstr "Owning service"

#: apps/node_man/models.py:1108 apps/node_man/models.py:1311
msgid "工程名"
msgstr "Project name"

#: apps/node_man/models.py:1109
msgid "包大小"
msgstr "Packet size"

#: apps/node_man/models.py:1110
msgid "包路径"
msgstr "Package path"

#: apps/node_man/models.py:1111
msgid "md5值"
msgstr "Md5 value"

#: apps/node_man/models.py:1112
msgid "包更新时间"
msgstr "Package update time"

#: apps/node_man/models.py:1113
msgid "包创建时间"
msgstr "Package created at"

#: apps/node_man/models.py:1114
msgid "安装包链接"
msgstr "Installation package link"

#: apps/node_man/models.py:1116 apps/node_man/models.py:1329
msgid "系统类型"
msgstr "System type"

#: apps/node_man/models.py:1123
msgid "CPU类型"
msgstr "CPU type"

#: apps/node_man/models.py:1127 apps/node_man/models.py:1680
msgid "是否已经发布版本"
msgstr "Is a version released"

#: apps/node_man/models.py:1129
msgid "插件是否可用"
msgstr "Is plugin available"

#: apps/node_man/models.py:1131
msgid "版本日志"
msgstr "Version log"

#: apps/node_man/models.py:1132
msgid "英文版本日志"
msgstr "English version log"

#: apps/node_man/models.py:1177
msgid "找不到可导出插件，请确认后重试"
msgstr "No exportable plugin found, please confirm and try again"

#: apps/node_man/models.py:1253
msgid "插件包不存在，请联系管理员处理"
msgstr ""
"The plugin package does not exist, please contact the administrator to deal "
"with it"

#: apps/node_man/models.py:1291 apps/node_man/models.py:1292
msgid "插件包（Packages）"
msgstr "Packages"

#: apps/node_man/models.py:1310 apps/node_man/models.py:1419
msgid "模块名"
msgstr "Module name"

#: apps/node_man/models.py:1312
msgid "记录对应的插件包ID"
msgstr "Record the corresponding plugin package ID"

#: apps/node_man/models.py:1314
msgid "安装路径"
msgstr "Install path"

#: apps/node_man/models.py:1319
msgid "启动命令"
msgstr "Start command"

#: apps/node_man/models.py:1320
msgid "停止命令"
msgstr "Stop command"

#: apps/node_man/models.py:1321
msgid "重启命令"
msgstr "Restart command"

#: apps/node_man/models.py:1322
msgid "重载命令"
msgstr "Reload command"

#: apps/node_man/models.py:1323
msgid "kill命令"
msgstr "Kill command"

#: apps/node_man/models.py:1324
msgid "进程版本查询命令"
msgstr "Process version query command"

#: apps/node_man/models.py:1325
msgid "进程健康检查命令"
msgstr "Process health check command"

#: apps/node_man/models.py:1326
msgid "调试进程命令"
msgstr "Debug process command"

#: apps/node_man/models.py:1331
msgid "实际二进制执行文件名"
msgstr "Actual binary executable filename"

#: apps/node_man/models.py:1332
msgid "插件允许使用的端口范围，格式 1,3,6-8,10-100"
msgstr "The range of ports allowed by the plugin, in the format 1,3,6-8,10-100"

#: apps/node_man/models.py:1333
msgid "是否需要托管"
msgstr "Do you need hosting"

#: apps/node_man/models.py:1352
#, python-format
msgid "无法解析的端口号：%s"
msgstr "Unresolved port number: %s"

#: apps/node_man/models.py:1357
#, python-format
msgid "不在合法范围内的端口号：%s"
msgstr "Port number not in valid range: %s"

#: apps/node_man/models.py:1388 apps/node_man/models.py:1397
#, python-format
msgid "不合法的端口范围定义格式：%s"
msgstr "Illegal port range definition format: %s"

#: apps/node_man/models.py:1401
#, python-format
msgid "端口范围字符串解析失败：%s"
msgstr "Failed to parse port range string: %s"

#: apps/node_man/models.py:1406 apps/node_man/models.py:1407
msgid "插件控制表（ProcControl）"
msgstr "Plugin Control Table (ProcControl)"

#: apps/node_man/models.py:1418
msgid "上传包文件名"
msgstr "Upload package filename"

#: apps/node_man/models.py:1421
msgid "文件上传的路径名"
msgstr "File upload pathname"

#: apps/node_man/models.py:1422
msgid "文件大小，单位Byte"
msgstr "File size, in Bytes"

#: apps/node_man/models.py:1423
msgid "文件MD5"
msgstr "File MD5"

#: apps/node_man/models.py:1425
msgid "文件上传时间"
msgstr "File upload time"

#: apps/node_man/models.py:1426
msgid "上传用户名"
msgstr "Upload Username"

#: apps/node_man/models.py:1427 apps/node_man/models.py:1540
#: apps/node_man/models.py:1684 apps/node_man/models.py:1768
msgid "来源系统app code"
msgstr "Source system app code"

#: apps/node_man/models.py:1430
msgid "文件包上传记录"
msgstr "Package upload log"

#: apps/node_man/models.py:1431
msgid "文件包上传记录表"
msgstr "Package upload record table"

#: apps/node_man/models.py:1460
#, python-brace-format
msgid "文件{file_path}不存在，请确认后重试"
msgstr "The file {file_path} does not exist, please confirm and try again"

#: apps/node_man/models.py:1493
msgid "文件迁移失败，请联系管理员协助处理"
msgstr "File migration failed, please contact administrator for assistance"

#: apps/node_man/models.py:1517
msgid "任务准备中"
msgstr "Task preparation"

#: apps/node_man/models.py:1518
msgid "任务进行中"
msgstr "Task in progress"

#: apps/node_man/models.py:1519
msgid "任务已完成"
msgstr "Task completed"

#: apps/node_man/models.py:1526
msgid "gse插件"
msgstr "Gse plugin"

#: apps/node_man/models.py:1530
msgid "下载文件类型"
msgstr "Download file type"

#: apps/node_man/models.py:1531
msgid "下载查询参数"
msgstr "Download query parameters"

#: apps/node_man/models.py:1533
msgid "打包后的文件路径"
msgstr "Packaged file path"

#: apps/node_man/models.py:1535
msgid "任务错误信息"
msgstr "Task error message"

#: apps/node_man/models.py:1537
msgid "任务创建者"
msgstr "Task creator"

#: apps/node_man/models.py:1538
msgid "下载任务创建时间"
msgstr "Download task created at"

#: apps/node_man/models.py:1539
msgid "任务完成时间"
msgstr "Task completion time"

#: apps/node_man/models.py:1630
#, python-brace-format
msgid "任务失败: {err_msg}"
msgstr "Task failed: {err_msg}"

#: apps/node_man/models.py:1646 apps/node_man/models.py:1647
msgid "下载记录"
msgstr "Download log"

#: apps/node_man/models.py:1652 apps/node_man/serializers/plugin_v2.py:246
msgid "CPU限额"
msgstr "CPU quota"

#: apps/node_man/models.py:1653 apps/node_man/serializers/plugin_v2.py:247
msgid "内存限额"
msgstr "Memory quota"

#: apps/node_man/models.py:1656
msgid "CMDB实例ID"
msgstr "CMDB instance ID"

#: apps/node_man/models.py:1661 apps/node_man/models.py:1662
msgid "插件资源设置"
msgstr "Plugin Resource Settings"

#: apps/node_man/models.py:1673
msgid "配置模板名"
msgstr "Config template name"

#: apps/node_man/models.py:1674
msgid "配置模板版本"
msgstr "Config Template Version"

#: apps/node_man/models.py:1675
msgid "是否主配置"
msgstr "Is the main configuration"

#: apps/node_man/models.py:1677
msgid "文件格式"
msgstr "File format"

#: apps/node_man/models.py:1678
msgid "文件在该插件目录中相对路径"
msgstr "Relative path to file in this plugin directory"

#: apps/node_man/models.py:1679
msgid "配置内容"
msgstr "Config content"

#: apps/node_man/models.py:1682 apps/node_man/models.py:1766
#: apps/utils/orm.py:175 pipeline/contrib/periodic_task/models.py:296
#: pipeline/models.py:276 pipeline/models.py:616
msgid "创建者"
msgstr "Created by"

#: apps/node_man/models.py:1694
msgid "插件配置文件模板"
msgstr "Plugin configuration file template"

#: apps/node_man/models.py:1695
msgid "插件配置文件模板表"
msgstr "Plugin configuration file template table"

#: apps/node_man/models.py:1763
msgid "对应实例记录ID"
msgstr "Corresponding instance record ID"

#: apps/node_man/models.py:1764
msgid "渲染参数"
msgstr "Render parameters"

#: apps/node_man/models.py:1765
msgid "渲染参数MD5"
msgstr "Render parameter MD5"

#: apps/node_man/models.py:1827
msgid "插件配置文件实例"
msgstr "Plugin config file instance"

#: apps/node_man/models.py:1828
msgid "插件配置文件实例表"
msgstr "Plugin configuration file instance table"

#: apps/node_man/models.py:1835
msgid "顺序"
msgstr "Order"

#: apps/node_man/models.py:1836
msgid "步骤ID"
msgstr "Step ID"

#: apps/node_man/models.py:1837
msgid "步骤类型"
msgstr "Step type"

#: apps/node_man/models.py:1838
msgid "配置"
msgstr "Config"

#: apps/node_man/models.py:1839
msgid "参数"
msgstr "Parameter"

#: apps/node_man/models.py:1855 apps/node_man/models.py:1856
msgid "订阅步骤"
msgstr "Subscribe step"

#: apps/node_man/models.py:1877
msgid "服务"
msgstr "Service"

#: apps/node_man/models.py:1887
msgid "动态实例（拓扑）"
msgstr "Dynamic instance (topology)"

#: apps/node_man/models.py:1888
msgid "静态实例"
msgstr "Static instance"

#: apps/node_man/models.py:1889
msgid "服务模板"
msgstr "Service template"

#: apps/node_man/models.py:1890
msgid "集群模板"
msgstr "Cluster template"

#: apps/node_man/models.py:1901
msgid "一次性订阅"
msgstr "Once subscription"

#: apps/node_man/models.py:1906
msgid "任务名称"
msgstr "Task name"

#: apps/node_man/models.py:1908 apps/node_man/models.py:2451
msgid "对象类型"
msgstr "Object type"

#: apps/node_man/models.py:1910
msgid "节点"
msgstr "Node"

#: apps/node_man/models.py:1911
msgid "下发的目标机器"
msgstr "Destination machine issued"

#: apps/node_man/models.py:1912
msgid "所属系统"
msgstr "Owning system"

#: apps/node_man/models.py:1918 apps/node_man/serializers/policy.py:147
msgid "订阅类别"
msgstr "Subscription Category"

#: apps/node_man/models.py:1919 apps/node_man/serializers/plugin.py:205
#: apps/node_man/serializers/plugin_v2.py:173
#: apps/node_man/serializers/plugin_v2.py:194
#: apps/node_man/serializers/plugin_v2.py:245
#: apps/node_man/serializers/policy.py:63
#: apps/node_man/serializers/policy.py:153
msgid "插件名称"
msgstr "Plugin name"

#: apps/node_man/models.py:1922
msgid "父订阅ID"
msgstr "Parent subscription id"

#: apps/node_man/models.py:2214 apps/node_man/models.py:2215
msgid "订阅（Subscription）"
msgstr "Subscription"

#: apps/node_man/models.py:2229
msgid "执行范围"
msgstr "Execution scope"

#: apps/node_man/models.py:2230
msgid "不同step执行的动作名称。键值对"
msgstr "Name of the action performed by different steps. Key-value pair"

#: apps/node_man/models.py:2232
msgid "错误信息"
msgstr "Error message"

#: apps/node_man/models.py:2233
msgid "是否准备就绪"
msgstr "Ready"

#: apps/node_man/models.py:2235 apps/node_man/models.py:2276
msgid "Pipeline ID"
msgstr "Pipeline ID"

#: apps/node_man/models.py:2255 apps/node_man/models.py:2256
msgid "订阅任务"
msgstr "Subscribe tasks"

#: apps/node_man/models.py:2274 apps/node_man/models.py:2452
msgid "实例信息"
msgstr "Instance info"

#: apps/node_man/models.py:2275
msgid "步骤信息"
msgstr "Step info"

#: apps/node_man/models.py:2277
msgid "Start Pipeline ID"
msgstr "Start Pipeline ID"

#: apps/node_man/models.py:2280
msgid "是否需要清洗临时信息"
msgstr "Do you need to clean temporary information"

#: apps/node_man/models.py:2281
msgid "是否为实例最新记录"
msgstr "Is the latest record for the instance"

#: apps/node_man/models.py:2344 apps/node_man/models.py:2368
msgid "步骤ID [{}] 在该订阅配置中不存在"
msgstr "Step id [{}] does not exist in this subscription configuration"

#: apps/node_man/models.py:2397 apps/node_man/models.py:2398
msgid "订阅实例记录"
msgstr "Subscribe instance records"

#: apps/node_man/models.py:2402
msgid "作业实例ID"
msgstr "Job instance id"

#: apps/node_man/models.py:2403
msgid "订阅实例ID列表"
msgstr "Subscription instance ID list"

#: apps/node_man/models.py:2404 pipeline/contrib/statistics/models.py:21
#: pipeline/contrib/statistics/models.py:37
msgid "节点ID"
msgstr "Node ID"

#: apps/node_man/models.py:2405
msgid "作业状态"
msgstr "Job status"

#: apps/node_man/models.py:2408 apps/node_man/models.py:2409
msgid "作业平台ID映射表"
msgstr "Job platform ID mapping table"

#: apps/node_man/models.py:2413 apps/node_man/models.py:2430
msgid "订阅实例ID"
msgstr "Subscription instance ID"

#: apps/node_man/models.py:2414 apps/node_man/models.py:2435
#: pipeline/log/models.py:45
msgid "日志内容"
msgstr "Log content"

#: apps/node_man/models.py:2415 pipeline/log/models.py:44
msgid "日志等级"
msgstr "Log level"

#: apps/node_man/models.py:2419 apps/node_man/models.py:2420
msgid "订阅实例日志"
msgstr "Subscription instance log"

#: apps/node_man/models.py:2431
msgid "Pipeline原子ID"
msgstr "Pipeline Atom ID"

#: apps/node_man/models.py:2440 apps/node_man/models.py:2441
msgid "订阅实例状态表"
msgstr "Subscription instance status table"

#: apps/node_man/models.py:2449 apps/node_man/models.py:2493
msgid "事件类型"
msgstr "Event type"

#: apps/node_man/models.py:2450
msgid "动作"
msgstr "Action"

#: apps/node_man/models.py:2456 apps/node_man/models.py:2457
msgid "CMDB事件记录"
msgstr "CMDB event log"

#: apps/node_man/models.py:2465
msgid "PipelineID"
msgstr "PipelineID"

#: apps/node_man/models.py:2466
msgid "Pipeline拓扑树"
msgstr "Pipeline topology tree"

#: apps/node_man/models.py:2492
msgid "游标"
msgstr "Cursor"

#: apps/node_man/models.py:2494
msgid "资源"
msgstr "Resource"

#: apps/node_man/models.py:2495
msgid "事件详情"
msgstr "Event Details"

#: apps/node_man/models.py:2499 apps/node_man/models.py:2500
msgid "CMDB资源监听事件"
msgstr "CMDB resource listener event"

#: apps/node_man/serializers/ap.py:87 apps/node_man/serializers/job.py:124
msgid "请求参数 inner_ip 和 inner_ipv6 不能同时为空"
msgstr ""
"Request parameters inner_ip and inner_ipv6 cannot be empty at the same time"

#: apps/node_man/serializers/ap.py:89
msgid "请求参数 outer_ip 和 outer_ipv6 不能同时为空"
msgstr ""
"Request parameters outer_ip and outer_ipv6 cannot be empty at the same time"

#: apps/node_man/serializers/ap.py:93
msgid "ZK IP地址"
msgstr "ZK IP address"

#: apps/node_man/serializers/ap.py:94
msgid "ZK 端口"
msgstr "ZK port"

#: apps/node_man/serializers/ap.py:101
msgid "ZK密码"
msgstr "ZK password"

#: apps/node_man/serializers/ap.py:102
msgid "Agent配置"
msgstr "Agent Configuration"

#: apps/node_man/serializers/ap.py:107
msgid "BSCP配置"
msgstr "BSCP configuration"

#: apps/node_man/serializers/base.py:34
#: apps/node_man/serializers/plugin_v2.py:202
#, python-brace-format
msgid "不存在名称为: {name} 的插件"
msgstr "No plugin with name: {name}"

#: apps/node_man/serializers/base.py:42
msgid "configs item must be contains (os, cpu_arch, version)"
msgstr "Configs item must be contains (os, cpu_arch, version)"

#: apps/node_man/serializers/base.py:44
msgid "相同os & cpu_arch 组合至多选择一个包"
msgstr "Select at most one package for the same os &amp; cpu_arch combination"

#: apps/node_man/serializers/base.py:66
msgid "节点缺少 bk_biz_id 属性"
msgstr "Node is missing bk_biz_id attribute"

#: apps/node_man/serializers/base.py:71
msgid ""
"部署节点属性组合必须是 (bk_biz_id, bk_host_id) 或者 (bk_biz_id, bk_obj_id, "
"bk_inst_id)"
msgstr ""
"Deployment node attribute combination must be (bk_biz_id, bk_host_id) or "
"(bk_biz_id, bk_obj_id, bk_inst_id)"

#: apps/node_man/serializers/base.py:76
#, python-brace-format
msgid "部署节点[{node}]不是主机实例"
msgstr "Deployment node [{node}] is not a host instance"

#: apps/node_man/serializers/base.py:80
#, python-brace-format
msgid "部署节点[{node}不是拓扑节点]"
msgstr "Deployment node [{node} is not a topology node]"

#: apps/node_man/serializers/base.py:88 apps/node_man/serializers/cmdb.py:53
#: apps/node_man/serializers/job.py:71 apps/node_man/serializers/job.py:390
#: apps/node_man/serializers/policy.py:117
#: apps/node_man/serializers/policy.py:141
msgid "当前页数"
msgstr "Current page number"

#: apps/node_man/serializers/base.py:93
msgid "只返回IP"
msgstr "Only return IP"

#: apps/node_man/serializers/base.py:95
msgid "仅返回的字段"
msgstr "Fields returned only"

#: apps/node_man/serializers/base.py:103 apps/node_man/serializers/host.py:74
#: apps/node_man/serializers/job.py:281 apps/node_man/serializers/plugin.py:235
msgid "跨页全选排除主机"
msgstr "Select all exclude hosts across pages"

#: apps/node_man/serializers/cloud.py:20
msgid "是否返回直连区域"
msgstr "Whether to return the \"Direct Mode\""

#: apps/node_man/serializers/cmdb.py:23
msgid "操作"
msgstr "Action"

#: apps/node_man/serializers/cmdb.py:48
msgid "搜索关键字"
msgstr "Search keyword"

#: apps/node_man/serializers/cmdb.py:50 apps/node_man/serializers/job.py:289
#: apps/node_man/serializers/policy.py:61
msgid "业务ID列表"
msgstr "List of business IDs"

#: apps/node_man/serializers/cmdb.py:54 apps/node_man/serializers/job.py:72
#: apps/node_man/serializers/job.py:391 apps/node_man/serializers/policy.py:118
#: apps/node_man/serializers/policy.py:142
msgid "分页大小"
msgstr "Pagination Size"

#: apps/node_man/serializers/debug.py:16 apps/node_man/serializers/debug.py:20
msgid "订阅任务ID"
msgstr "Subscription task ID"

#: apps/node_man/serializers/host.py:23
#: pipeline/contrib/periodic_task/models.py:299
msgid "额外信息"
msgstr "Extra info"

#: apps/node_man/serializers/host.py:24
msgid "正在运行机器数"
msgstr "Number of running machines"

#: apps/node_man/serializers/host.py:45
msgid "账号"
msgstr "Account"

#: apps/node_man/serializers/host.py:46
msgid "端口号"
msgstr "Port"

#: apps/node_man/serializers/host.py:50
msgid "秘钥"
msgstr "Secret key"

#: apps/node_man/serializers/host.py:51 apps/node_man/serializers/job.py:112
msgid "加速设置"
msgstr "Acceleration Settings"

#: apps/node_man/serializers/host.py:52
msgid "加速"
msgstr "Speed up"

#: apps/node_man/serializers/host.py:70
msgid "是否针对代理"
msgstr "For proxy"

#: apps/node_man/serializers/host.py:79 apps/node_man/serializers/plugin.py:255
msgid "跨页全选模式下不允许传bk_host_id参数."
msgstr "The bk_host_id parameter is not allowed in select-all across pages."

#: apps/node_man/serializers/host.py:82
msgid "不允许对代理执行跨页全选模式."
msgstr "Spread select all mode is not allowed for proxy."

#: apps/node_man/serializers/host.py:85 apps/node_man/serializers/job.py:304
#: apps/node_man/serializers/plugin.py:257
msgid "必须选择一种模式(【是否跨页全选】)"
msgstr "A mode must be selected ([whether to select all across pages])"

#: apps/node_man/serializers/host_v2.py:27
msgid "主机范围"
msgstr "Host range"

#: apps/node_man/serializers/host_v2.py:28
#: apps/node_man/serializers/host_v2.py:34
#: apps/node_man/serializers/plugin_v2.py:174
msgid "拓扑节点列表"
msgstr "Topology node list"

#: apps/node_man/serializers/host_v2.py:29
msgid "仅返回Agent统计状态"
msgstr "Only return Agent statistics status"

#: apps/node_man/serializers/host_v2.py:30
msgid "返回所有节点类型"
msgstr "Return all node types"

#: apps/node_man/serializers/host_v2.py:46
msgid "查询节点属性组合必须为(bk_biz_id, bk_inst_id, bk_obj_id)"
msgstr ""
"The query node attribute combination must be (bk_biz_id, bk_inst_id, "
"bk_obj_id)"

#: apps/node_man/serializers/iam.py:24 pipeline/models.py:615
msgid "实例名称"
msgstr "Instance name"

#: apps/node_man/serializers/iam.py:28
msgid "申请权限信息"
msgstr "Request permission information"

#: apps/node_man/serializers/install_channel.py:30
msgid "安装通道名称"
msgstr "Installation channel name"

#: apps/node_man/serializers/install_channel.py:31
msgid "跳板机节点"
msgstr "Springboard node"

#: apps/node_man/serializers/job.py:62 apps/node_man/serializers/policy.py:24
msgid "排序字段"
msgstr "Sort by field"

#: apps/node_man/serializers/job.py:63 apps/node_man/serializers/policy.py:25
msgid "排序类型"
msgstr "Sort by type"

#: apps/node_man/serializers/job.py:73
msgid "排序"
msgstr "Sort by"

#: apps/node_man/serializers/job.py:74
msgid "隐藏自动部署任务"
msgstr "Hide automatic deployment tasks"

#: apps/node_man/serializers/job.py:76
msgid "任务类型列表"
msgstr "Task type list"

#: apps/node_man/serializers/job.py:77
msgid "操作类型列表"
msgstr "Operation type list"

#: apps/node_man/serializers/job.py:78
msgid "策略名称列表"
msgstr "List of policy names"

#: apps/node_man/serializers/job.py:81
msgid "起始时间"
msgstr "Start time"

#: apps/node_man/serializers/job.py:82
msgid "终止时间"
msgstr "Termination time"

#: apps/node_man/serializers/job.py:96
msgid "安装通道ID"
msgstr "Installation channel ID"

#: apps/node_man/serializers/job.py:106
msgid "账户"
msgstr "Account"

#: apps/node_man/serializers/job.py:110
msgid "是否手动模式"
msgstr "Whether manual mode"

#: apps/node_man/serializers/job.py:111
msgid "密码保留天数"
msgstr "Password retention days"

#: apps/node_man/serializers/job.py:113
msgid "传输限速"
msgstr "Transfer rate limit"

#: apps/node_man/serializers/job.py:115
msgid "是否需要注入ap_id到meta"
msgstr "Whether need to inject AP_ID to meta"

#: apps/node_man/serializers/job.py:126
msgid "Proxy 操作的请求参数 outer_ip 和 outer_ipv6 不能同时为空"
msgstr ""
"The request parameters outer_ip and outer_ipv6 of the Proxy operation cannot "
"be empty at the same time"

#: apps/node_man/serializers/job.py:139
#, python-brace-format
msgid "{op_type} 操作必须传入 bk_host_id 参数"
msgstr "{op_type} operation must fill in bk_host_id parameter"

#: apps/node_man/serializers/job.py:145
#, python-brace-format
msgid "{op_type} 操作必须填写认证类型"
msgstr "{op_type} operation requires authentication type."

#: apps/node_man/serializers/job.py:150
msgid "密码认证方式必须填写密码"
msgstr "Password authentication method must fill in password"

#: apps/node_man/serializers/job.py:152
msgid "密钥认证方式必须上传密钥"
msgstr "Key authentication method must upload key"

#: apps/node_man/serializers/job.py:154
msgid "必须上传账号和端口"
msgstr "Must upload account and port"

#: apps/node_man/serializers/job.py:159
msgid "直连区域必须填写Ap_id"
msgstr "Ap_id must be filled in the \"Direct Mode\"."

#: apps/node_man/serializers/job.py:177
msgid "脚本名称"
msgstr "Script name"

#: apps/node_man/serializers/job.py:181 apps/node_man/serializers/job.py:275
msgid "Agent 设置信息"
msgstr "Agent configuration information"

#: apps/node_man/serializers/job.py:183
msgid "主机信息"
msgstr "Host info"

#: apps/node_man/serializers/job.py:184
msgid "被替换的Proxy主机ID"
msgstr "Replaced proxy host ID"

#: apps/node_man/serializers/job.py:185 apps/node_man/serializers/job.py:282
msgid "是否安装最新版本插件"
msgstr "Whether to install the latest version plugin"

#: apps/node_man/serializers/job.py:186 apps/node_man/serializers/job.py:283
msgid "是否为安装额外Agent"
msgstr "Whether to install additional agent"

#: apps/node_man/serializers/job.py:187
msgid "脚本钩子列表"
msgstr "Script Hook List"

#: apps/node_man/serializers/job.py:201
msgid "替换PROXY必须填写replace_host_id"
msgstr "Replace PROXY must fill in replace_host_id"

#: apps/node_man/serializers/job.py:220
msgid "主机信息缺少主机ID（bk_host_id）"
msgstr "Host information is missing the Host ID （bk_host_id)"

#: apps/node_man/serializers/job.py:222
msgid "主机信息缺少业务ID（bk_biz_id）"
msgstr "Host information is missing the business ID (bk_biz_id)"

#: apps/node_man/serializers/job.py:286 apps/node_man/serializers/plugin.py:238
msgid "操作类型,"
msgstr "Operation type,"

#: apps/node_man/serializers/job.py:288
msgid "主机ID列表"
msgstr "List of host IDs"

#: apps/node_man/serializers/job.py:297
msgid "该接口不可用于安装"
msgstr "The interface is not available for installation."

#: apps/node_man/serializers/job.py:299
msgid "该接口不可用于替换代理"
msgstr "This interface is unavailable for proxy replacement."

#: apps/node_man/serializers/job.py:302
msgid "跨页全选模式下不允许传bk_host_id参数"
msgstr "The bk_host_id parameter is not allowed in select-all across pages"

#: apps/node_man/serializers/job.py:392
msgid "开始位置参数优先page使用"
msgstr "Starting position parameter prioritizes page usage"

#: apps/node_man/serializers/job.py:401
msgid "任务实例ID列表"
msgstr "Task instance id list"

#: apps/node_man/serializers/job.py:405
msgid "任务实例ID"
msgstr "Task instance id"

#: apps/node_man/serializers/meta.py:22
msgid "安装P-Agent超时时间"
msgstr "Install P-Agent timeout"

#: apps/node_man/serializers/meta.py:23
msgid "安装Agent超时时间"
msgstr "Install Agent Timeout"

#: apps/node_man/serializers/meta.py:24
msgid "安装Proxy超时时间"
msgstr "Install Proxy timeout"

#: apps/node_man/serializers/meta.py:25
msgid "安装下载限速"
msgstr "Installation download speed limit"

#: apps/node_man/serializers/meta.py:26
msgid "并行安装数"
msgstr "Number of parallel installs"

#: apps/node_man/serializers/meta.py:27
msgid "节点管理日志级别"
msgstr "Node admin log level"

#: apps/node_man/serializers/plugin.py:198
msgid "仅返回概要信息(bk_host_id, bk_biz_id)"
msgstr "Only return summary information (bk_host_id, bk_biz_id)"

#: apps/node_man/serializers/plugin.py:199
msgid "是否节点详情"
msgstr "Is node details"

#: apps/node_man/serializers/plugin.py:201
msgid "是否返回Agent状态统计信息"
msgstr "Whether to return agent status statistics"

#: apps/node_man/serializers/plugin.py:206
#: pipeline/contrib/statistics/models.py:24
#: pipeline/contrib/statistics/models.py:46
msgid "插件版本"
msgstr "Plugin version"

#: apps/node_man/serializers/plugin.py:207
msgid "保留原有配置"
msgstr "Keep old configuration"

#: apps/node_man/serializers/plugin.py:208
msgid "不重启进程"
msgstr "Do not restart process"

#: apps/node_man/serializers/plugin.py:230
msgid "插件信息"
msgstr "Plugin info"

#: apps/node_man/serializers/plugin.py:232
msgid "插件信息列表"
msgstr "Plugin info list"

#: apps/node_man/serializers/plugin.py:248
msgid "插件参数 plugin_params 和 plugin_params_list 参数不能同时为空"
msgstr ""
"Plugin parameters plugin_params and plugin_params_list parameters cannot "
"both be empty"

#: apps/node_man/serializers/plugin.py:260
msgid "插件管理只允许对插件进行操作."
msgstr "Plugin management only allows operations on plugins."

#: apps/node_man/serializers/plugin.py:264
msgid "不允许选择重复的插件进行操作"
msgstr "Selecting duplicate plugins for operation is not allowed"

#: apps/node_man/serializers/plugin_v2.py:24
msgid "插件别名"
msgstr "Plugin alias"

#: apps/node_man/serializers/plugin_v2.py:165
msgid "仅支持'tgz', 'tar.gz'拓展名的文件"
msgstr "Only files with extension 'tgz', 'tar.gz' are supported"

#: apps/node_man/serializers/plugin_v2.py:178
#, python-brace-format
msgid "插件[{project}] 不存在"
msgstr "Plugin [{project}] does not exist"

#: apps/node_man/serializers/plugin_v2.py:184
msgid "配置模板ID列表"
msgstr "Config template ID list"

#: apps/node_man/serializers/plugin_v2.py:205
msgid "插件安装/更新需要传递steps"
msgstr "Plugin install/update requires passing steps"

#: apps/node_man/serializers/plugin_v2.py:211
msgid "插件名称列表"
msgstr "List of plugin names"

#: apps/node_man/serializers/plugin_v2.py:213
msgid "聚合关键字，可选：os/version/cpu_arch"
msgstr "Aggregate keyword, optional: os/version/cpu_arch"

#: apps/node_man/serializers/plugin_v2.py:227
#, python-brace-format
msgid "存在非法关键字 -> {illegal_keywords}, 可选项为 -> {optional_keys}"
msgstr ""
"Illegal keywords exist -> {illegal_keywords}, optional options are -> "
"{optional_keys}"

#: apps/node_man/serializers/plugin_v2.py:236
msgid "对象ID"
msgstr "Object ID"

#: apps/node_man/serializers/policy.py:28
msgid "仅搜索父策略"
msgstr "Only searching for root policy"

#: apps/node_man/serializers/policy.py:32
msgid "排序方式"
msgstr "Sort by"

#: apps/node_man/serializers/policy.py:46
#, python-brace-format
msgid ""
"不支持的排序字段 -> {order_by_field}, 全部可排序字段 -> {policy_head_tuple}"
msgstr ""
"No supporting sorting fields -> {order_by_field}, all sort by fields -> "
"{policy_head_tuple}"

#: apps/node_man/serializers/policy.py:64
msgid "模糊搜索关键字，支持策略名称、插件名称的模糊搜索"
msgstr ""
"Fuzzy search keyword, support fuzzy search of policy name and plugin name"

#: apps/node_man/serializers/policy.py:65
msgid "是否采取懒加载策略（仅返回一级节点）"
msgstr "Whether to adopt lazy loading strategy (only return first-level nodes)"

#: apps/node_man/serializers/policy.py:75
msgid "所属父策略"
msgstr "Owning parent policy"

#: apps/node_man/serializers/policy.py:87
msgid "灰度策略与父策略所部署插件不一致"
msgstr ""
"The grayscale policy is inconsistent with the plugin deployed by the parent "
"policy"

#: apps/node_man/serializers/policy.py:90
msgid "灰度策略不能作为父策略"
msgstr "Grayscale policy cannot be used as parent policy"

#: apps/node_man/serializers/policy.py:93
msgid "灰度策略仅支持静态IP目标"
msgstr "Grayscale policy only supports static IP targets"

#: apps/node_man/serializers/policy.py:99
msgid "灰度策略目标不在父策略范围内"
msgstr "Grayscale policy target is not within the scope of parent policy"

#: apps/node_man/serializers/policy.py:119
#: apps/node_man/serializers/policy.py:136
#: apps/node_man/serializers/policy.py:167
#: apps/node_man/serializers/policy.py:183
msgid "策略ID"
msgstr "Policy ID"

#: apps/node_man/serializers/policy.py:132
msgid "policy_id, scope 必须其中一个存在"
msgstr "policy_id, scope must exist"

#: apps/node_man/serializers/policy.py:169
msgid "仅停用策略"
msgstr "Deactivate policy only"

#: apps/node_man/serializers/policy.py:170
msgid "父策略ID"
msgstr "Parent Policy ID"

#: apps/node_man/serializers/policy.py:183
msgid "策略ID列表"
msgstr "List of policy IDs"

#: apps/node_man/serializers/response.py:24
#: apps/node_man/serializers/response.py:33
msgid "作业任务ID"
msgstr "Job task ID"

#: apps/node_man/serializers/response.py:25
msgid "失败的主机信息"
msgstr "Failed host information"

#: apps/node_man/serializers/response.py:34
msgid "作业任务历史跳转URL"
msgstr "Job task history jump URL"

#: apps/node_man/tools/host.py:56
#, python-brace-format
msgid "密文无法解密，请检查是否按规则使用密钥加密：{err_msg}"
msgstr ""
"The ciphertext cannot be decrypted, please check whether the key is "
"encrypted according to the rules: {err_msg}"

#: apps/node_man/tools/host.py:58
msgid "密文解密失败：{err_msg"
msgstr "Ciphertext decryption failed: {err_msg"

#: apps/node_man/tools/job.py:37
msgid "节点异常"
msgstr "Node exception"

#: apps/node_man/tools/job.py:43
#, python-brace-format
msgid "正在 {node_name}"
msgstr "On {node_name}"

#: apps/node_man/tools/job.py:49
#, python-brace-format
msgid "{node_name} 失败"
msgstr "{node_name} failed"

#: apps/node_man/tools/job.py:65
msgid "{}"
msgstr "{}"

#: apps/node_man/views/ap.py:375
#, python-brace-format
msgid "该接入点正在被「管控区域」 {cloud_names} 使用"
msgstr "This access point is being used by \"BK-Net\" {cloud_names}"

#: apps/node_man/views/meta.py:120
msgid "您没有权限修改任务配置"
msgstr "You do not have permission to modify task configuration"

#: apps/node_man/views/permission.py:57 common/api/modules/bk_node.py:19
#: config/default.py:327
msgid "节点管理"
msgstr "NodeMan"

#: apps/utils/drf.py:206
msgid "请求参数必须全部为整数"
msgstr "Request parameters must all be integers"

#: apps/utils/orm.py:99
msgid "删除时间"
msgstr "Delete time"

#: apps/utils/orm.py:100
msgid "删除者"
msgstr "Deleted by"

#: apps/utils/orm.py:177 pipeline/models.py:278
msgid "修改者"
msgstr "Editor"

#: common/api/base.py:185
#, python-brace-format
msgid "[{module}]API请求异常：({error_message})"
msgstr "[{module}]API request exception: ({error_message})"

#: common/api/base.py:251
msgid "返回数据格式不正确，结果格式非json."
msgstr ""
"The format of the returned data is incorrect, the format of the result is "
"not json."

#: common/api/base.py:322
#, python-brace-format
msgid "[BKAPI] {info}"
msgstr "[BKAPI] {info}"

#: common/api/models.py:34 common/api/models.py:35
msgid "【平台日志】API调用日志"
msgstr "[Platform log] API call log"

#: common/api/modules/esb.py:27
msgid "ESB"
msgstr "ESB"

#: common/api/modules/gse.py:19
msgid "管控平台"
msgstr "GSE"

#: common/api/modules/gse_v2.py:19
#, fuzzy
msgid "管控平台 V2"
msgstr "GSE V2"

#: common/api/modules/job.py:19
msgid "作业平台"
msgstr "JOB"

#: common/api/modules/sops.py:19
msgid "标准运维"
msgstr "BK-SOPS"

#: common/context_processors.py:44
msgid "节点管理 | 蓝鲸智云"
msgstr "NodeMan | Tencent Blueking"

#: pipeline/component_framework/models.py:42
#: pipeline/contrib/statistics/models.py:19
#: pipeline/contrib/statistics/models.py:35
msgid "组件编码"
msgstr "Component encoding"

#: pipeline/component_framework/models.py:43
msgid "组件版本"
msgstr "Component version"

#: pipeline/component_framework/models.py:44
msgid "组件名称"
msgstr "Component name"

#: pipeline/component_framework/models.py:45
msgid "组件是否可用"
msgstr "Is the component available"

#: pipeline/component_framework/models.py:50
#: pipeline/component_framework/models.py:51
msgid "组件 Component"
msgstr "Component Component"

#: pipeline/contrib/external_plugins/models/base.py:78
msgid "包源名"
msgstr "Package source name"

#: pipeline/contrib/external_plugins/models/base.py:79
msgid "是否是从配置文件中读取的"
msgstr "Is it read from configuration file"

#: pipeline/contrib/external_plugins/models/base.py:80
msgid "模块配置"
msgstr "Module configuration"

#: pipeline/contrib/external_plugins/models/source.py:24
msgid "文件托管仓库链接"
msgstr "File hosting repository link"

#: pipeline/contrib/external_plugins/models/source.py:25
msgid "分支名"
msgstr "Branch name"

#: pipeline/contrib/external_plugins/models/source.py:47
msgid "对象存储服务地址"
msgstr "Object storage service address"

#: pipeline/contrib/external_plugins/models/source.py:48
msgid "bucket 名"
msgstr "Bucket name"

#: pipeline/contrib/external_plugins/models/source.py:49
msgid "access key"
msgstr "Access key"

#: pipeline/contrib/external_plugins/models/source.py:50
msgid "secret key"
msgstr "Secret key"

#: pipeline/contrib/external_plugins/models/source.py:78
msgid "文件系统路径"
msgstr "Filesystem path"

#: pipeline/contrib/periodic_task/models.py:35
msgid "Days"
msgstr "Days"

#: pipeline/contrib/periodic_task/models.py:36
msgid "Hours"
msgstr "Hours"

#: pipeline/contrib/periodic_task/models.py:37
msgid "Minutes"
msgstr "Minutes"

#: pipeline/contrib/periodic_task/models.py:38
msgid "Seconds"
msgstr "Seconds"

#: pipeline/contrib/periodic_task/models.py:39
msgid "Microseconds"
msgstr "Microseconds"

#: pipeline/contrib/periodic_task/models.py:45
msgid "every"
msgstr "Every"

#: pipeline/contrib/periodic_task/models.py:46
msgid "period"
msgstr "Period"

#: pipeline/contrib/periodic_task/models.py:49
#: pipeline/contrib/periodic_task/models.py:159
msgid "interval"
msgstr "Interval"

#: pipeline/contrib/periodic_task/models.py:50
msgid "intervals"
msgstr "Intervals"

#: pipeline/contrib/periodic_task/models.py:70
#, python-brace-format
msgid "every {0.period_singular}"
msgstr "Every {0.period_singular}"

#: pipeline/contrib/periodic_task/models.py:71
#, python-brace-format
msgid "every {0.every:d} {0.period}"
msgstr "Every {0.every:d} {0.period}"

#: pipeline/contrib/periodic_task/models.py:84
msgid "minute"
msgstr "Minute"

#: pipeline/contrib/periodic_task/models.py:85
msgid "hour"
msgstr "Hour"

#: pipeline/contrib/periodic_task/models.py:86
msgid "day of week"
msgstr "Day of week"

#: pipeline/contrib/periodic_task/models.py:87
msgid "day of month"
msgstr "Day of month"

#: pipeline/contrib/periodic_task/models.py:88
msgid "month of year"
msgstr "Month of year"

#: pipeline/contrib/periodic_task/models.py:92
#: pipeline/contrib/periodic_task/models.py:165
msgid "crontab"
msgstr "Crontab"

#: pipeline/contrib/periodic_task/models.py:93
msgid "crontabs"
msgstr "Crontabs"

#: pipeline/contrib/periodic_task/models.py:156
msgid "name"
msgstr "Name"

#: pipeline/contrib/periodic_task/models.py:156
msgid "Useful description"
msgstr "Useful description"

#: pipeline/contrib/periodic_task/models.py:157
msgid "task name"
msgstr "Task name"

#: pipeline/contrib/periodic_task/models.py:167
msgid "Use one of interval/crontab"
msgstr "Use one of interval/crontab"

#: pipeline/contrib/periodic_task/models.py:169
msgid "Arguments"
msgstr "Arguments"

#: pipeline/contrib/periodic_task/models.py:169
msgid "JSON encoded positional arguments"
msgstr "JSON encoded positional arguments"

#: pipeline/contrib/periodic_task/models.py:171
msgid "Keyword arguments"
msgstr "Keyword arguments"

#: pipeline/contrib/periodic_task/models.py:171
msgid "JSON encoded keyword arguments"
msgstr "JSON encoded keyword arguments"

#: pipeline/contrib/periodic_task/models.py:174
msgid "queue"
msgstr "Queue"

#: pipeline/contrib/periodic_task/models.py:174
msgid "Queue defined in CELERY_QUEUES"
msgstr "Queue defined in CELERY_QUEUES"

#: pipeline/contrib/periodic_task/models.py:176
msgid "exchange"
msgstr "Exchange"

#: pipeline/contrib/periodic_task/models.py:177
msgid "routing key"
msgstr "Routing key"

#: pipeline/contrib/periodic_task/models.py:178
msgid "expires"
msgstr "Expires"

#: pipeline/contrib/periodic_task/models.py:179
msgid "enabled"
msgstr "Enabled"

#: pipeline/contrib/periodic_task/models.py:183
msgid "description"
msgstr "Description"

#: pipeline/contrib/periodic_task/models.py:189
msgid "djcelery periodic task"
msgstr "Djcelery periodic task"

#: pipeline/contrib/periodic_task/models.py:190
msgid "djcelery periodic tasks"
msgstr "Djcelery periodic tasks"

#: pipeline/contrib/periodic_task/models.py:278
msgid "周期任务名称"
msgstr "Period task name"

#: pipeline/contrib/periodic_task/models.py:283
msgid "周期任务对应的模板"
msgstr "Template corresponding to periodic task"

#: pipeline/contrib/periodic_task/models.py:287
msgid "调度策略"
msgstr "Scheduling Policy"

#: pipeline/contrib/periodic_task/models.py:289
msgid "celery 周期任务实例"
msgstr "Celery periodic task instance"

#: pipeline/contrib/periodic_task/models.py:292
msgid "用于创建流程实例的结构数据"
msgstr "Structure data used to create process instances"

#: pipeline/contrib/periodic_task/models.py:294
msgid "执行次数"
msgstr "Executions"

#: pipeline/contrib/periodic_task/models.py:295
msgid "上次运行时间"
msgstr "Last Run"

#: pipeline/contrib/periodic_task/models.py:297
#: pipeline/contrib/periodic_task/models.py:395
#: pipeline/engine/models/core.py:594
msgid "流程优先级"
msgstr "Priority"

#: pipeline/contrib/periodic_task/models.py:298
#: pipeline/contrib/periodic_task/models.py:396
#: pipeline/engine/models/core.py:595
msgid "流程使用的队列名"
msgstr "Queue name"

#: pipeline/contrib/periodic_task/models.py:382
msgid "周期任务"
msgstr "Periodic task"

#: pipeline/contrib/periodic_task/models.py:387
msgid "Pipeline 实例"
msgstr "Pipeline instance"

#: pipeline/contrib/periodic_task/models.py:392 pipeline/log/models.py:46
msgid "异常信息"
msgstr "Exception information"

#: pipeline/contrib/periodic_task/models.py:393
#: pipeline/engine/models/core.py:883 pipeline/engine/models/core.py:990
msgid "开始时间"
msgstr "Start time"

#: pipeline/contrib/periodic_task/models.py:394
msgid "是否启动成功"
msgstr "Launch OK"

#: pipeline/contrib/statistics/models.py:20
#: pipeline/contrib/statistics/models.py:58 pipeline/models.py:273
#: pipeline/models.py:458
msgid "模板ID"
msgstr "Template ID"

#: pipeline/contrib/statistics/models.py:22
#: pipeline/contrib/statistics/models.py:38
msgid "是否子流程引用"
msgstr "sub-processes"

#: pipeline/contrib/statistics/models.py:23
#: pipeline/contrib/statistics/models.py:39
msgid "子流程堆栈"
msgstr "Stack sub-processes"

#: pipeline/contrib/statistics/models.py:23
#: pipeline/contrib/statistics/models.py:39
msgid "JSON 格式的列表"
msgstr "List JSON format"

#: pipeline/contrib/statistics/models.py:27
#: pipeline/contrib/statistics/models.py:28
msgid "Pipeline标准插件被引用数据"
msgstr "Referenced data of pipeline standard plugin"

#: pipeline/contrib/statistics/models.py:40
msgid "标准插件执行开始时间"
msgstr "Standard plugin start time"

#: pipeline/contrib/statistics/models.py:41
msgid "标准插件执行结束时间"
msgstr "Standard plugin end time"

#: pipeline/contrib/statistics/models.py:42
msgid "标准插件执行耗时(s)"
msgstr "Time-consuming standard plug (s)"

#: pipeline/contrib/statistics/models.py:43
#: pipeline/core/flow/activity/service_activity.py:32
msgid "是否执行成功"
msgstr "Whether success"

#: pipeline/contrib/statistics/models.py:44 pipeline/engine/models/core.py:880
#: pipeline/engine/models/core.py:993
msgid "是否跳过"
msgstr "Whether to skip"

#: pipeline/contrib/statistics/models.py:45
msgid "是否重试记录"
msgstr "Whether to retry the record"

#: pipeline/contrib/statistics/models.py:49
#: pipeline/contrib/statistics/models.py:50
msgid "Pipeline标准插件执行数据"
msgstr "Performs data of pipeline standard plugin"

#: pipeline/contrib/statistics/models.py:59
#: pipeline/contrib/statistics/models.py:73
msgid "标准插件总数"
msgstr "Standard plug-ins"

#: pipeline/contrib/statistics/models.py:60
#: pipeline/contrib/statistics/models.py:74
msgid "子流程总数"
msgstr "Total number of subprocesses"

#: pipeline/contrib/statistics/models.py:61
#: pipeline/contrib/statistics/models.py:75
msgid "网关总数"
msgstr "Gateway"

#: pipeline/contrib/statistics/models.py:64
#: pipeline/contrib/statistics/models.py:65
msgid "Pipeline模板引用数据"
msgstr "Reference data of pipeline template"

#: pipeline/contrib/statistics/models.py:78
#: pipeline/contrib/statistics/models.py:79
msgid "Pipeline实例引用数据"
msgstr "Reference data of pipeline instance"

#: pipeline/core/flow/activity/service_activity.py:32
msgid "执行结果"
msgstr "Results"

#: pipeline/core/flow/activity/service_activity.py:33
#: pipeline/engine/models/core.py:879 pipeline/engine/models/core.py:992
msgid "循环次数"
msgstr "Cycles"

#: pipeline/core/flow/activity/service_activity.py:33
msgid "循环执行次数"
msgstr "Cycles"

#: pipeline/django_signal_valve/models.py:44
msgid "信号模块名"
msgstr "Signal module name"

#: pipeline/django_signal_valve/models.py:45
msgid "信号属性名"
msgstr "Signal property name"

#: pipeline/django_signal_valve/models.py:46
msgid "信号参数"
msgstr "Signal parameters"

#: pipeline/engine/admin.py:124
msgid "打开所选的功能"
msgstr "Open the selected function"

#: pipeline/engine/admin.py:125
msgid "关闭所选的功能"
msgstr "Close selected function"

#: pipeline/engine/conf/function_switch.py:19
msgid ""
"用于冻结引擎, 冻结期间会屏蔽所有内部信号及暂停所有进程，同时拒绝所有流程控制"
"请求"
msgstr ""
"Used to freeze the engine. During freezing, all internal signals are blocked "
"and all processes are suspended.At the same time, all process control "
"requests are rejected."

#: pipeline/engine/models/core.py:53 pipeline/engine/models/core.py:616
#: pipeline/engine/models/core.py:932 pipeline/engine/models/core.py:988
#: pipeline/engine/models/core.py:1140 pipeline/engine/models/core.py:1172
#: pipeline/engine/models/core.py:1198 pipeline/engine/models/core.py:1229
#: pipeline/log/models.py:42
msgid "ID"
msgstr "ID"

#: pipeline/engine/models/core.py:54
msgid "pipeline 运行时数据"
msgstr "Pipeline runtime data"

#: pipeline/engine/models/core.py:208
msgid "Process ID"
msgstr "Process ID"

#: pipeline/engine/models/core.py:209
msgid "根 pipeline 的 ID"
msgstr "Root pipeline of ID"

#: pipeline/engine/models/core.py:210
msgid "当前推进到的节点的 ID"
msgstr "To promote current node ID"

#: pipeline/engine/models/core.py:211
msgid "遇到该 ID 的节点就停止推进"
msgstr "The encounter node ID is stopped advancing"

#: pipeline/engine/models/core.py:212
msgid "父 process 的 ID"
msgstr "The parent process ID"

#: pipeline/engine/models/core.py:213
msgid "收到子节点 ACK 的数量"
msgstr "Number of sub-nodes receive an ACK"

#: pipeline/engine/models/core.py:214
msgid "需要收到的子节点 ACK 的数量"
msgstr "The number of child nodes need to receive an ACK"

#: pipeline/engine/models/core.py:215
msgid "该 process 是否还有效"
msgstr "Whether the process is also effective"

#: pipeline/engine/models/core.py:216
msgid "该 process 是否正在休眠"
msgstr "Whether the process is sleeping"

#: pipeline/engine/models/core.py:217
msgid "该 process 是否被冻结"
msgstr "Whether the process is frozen"

#: pipeline/engine/models/core.py:617
msgid "祖先 ID"
msgstr "ID ancestors"

#: pipeline/engine/models/core.py:618
msgid "后代 ID"
msgstr "ID offspring"

#: pipeline/engine/models/core.py:619
msgid "距离"
msgstr "Distance"

#: pipeline/engine/models/core.py:875 pipeline/engine/models/core.py:923
#: pipeline/engine/models/core.py:1047 pipeline/engine/models/core.py:1230
#: pipeline/log/models.py:49
msgid "节点 ID"
msgstr "Node ID"

#: pipeline/engine/models/core.py:877
msgid "节点名称"
msgstr "Node Name"

#: pipeline/engine/models/core.py:878
msgid "重试次数"
msgstr "Number of retries"

#: pipeline/engine/models/core.py:881
msgid "是否出错后自动忽略"
msgstr "Whether after an error automatically ignored"

#: pipeline/engine/models/core.py:884
msgid "归档时间"
msgstr "Archive Time"

#: pipeline/engine/models/core.py:885
msgid "版本"
msgstr "Version"

#: pipeline/engine/models/core.py:886
msgid "上次状态更新的时间"
msgstr "Last time status updates"

#: pipeline/engine/models/core.py:924 pipeline/engine/models/core.py:933
msgid "输入数据"
msgstr "Input data"

#: pipeline/engine/models/core.py:925 pipeline/engine/models/core.py:934
msgid "输出数据"
msgstr "Output Data"

#: pipeline/engine/models/core.py:926 pipeline/engine/models/core.py:935
msgid "异常数据"
msgstr "Abnormal data"

#: pipeline/engine/models/core.py:941
msgid "自增ID"
msgstr "Increment ID"

#: pipeline/engine/models/core.py:942
msgid "回调服务ID"
msgstr "Callback Service ID"

#: pipeline/engine/models/core.py:943 pipeline/engine/models/core.py:1052
msgid "回调数据"
msgstr "Callback data"

#: pipeline/engine/models/core.py:989
msgid "节点 id"
msgstr "Node id"

#: pipeline/engine/models/core.py:991 pipeline/models.py:620
msgid "结束时间"
msgstr "End Time"

#: pipeline/engine/models/core.py:1046
msgid "ID 节点ID+version"
msgstr "ID Node ID + version"

#: pipeline/engine/models/core.py:1048
msgid "Pipeline 进程 ID"
msgstr "Pipeline Process ID"

#: pipeline/engine/models/core.py:1049
msgid "被调度次数"
msgstr "Scheduling times"

#: pipeline/engine/models/core.py:1050
msgid "是否是回调型调度"
msgstr "Whether it is scheduling a callback type"

#: pipeline/engine/models/core.py:1051
msgid "是否支持多次回调"
msgstr "Whether to support multiple callbacks"

#: pipeline/engine/models/core.py:1053
msgid "待调度服务"
msgstr "Be-scheduled services"

#: pipeline/engine/models/core.py:1054
msgid "是否已完成"
msgstr "Completed or not"

#: pipeline/engine/models/core.py:1055
msgid "Activity 的版本"
msgstr "Activity version"

#: pipeline/engine/models/core.py:1056
msgid "是否正在被调度"
msgstr "Whether it is scheduled"

#: pipeline/engine/models/core.py:1141
msgid "子流程 ID"
msgstr "Sub-process ID"

#: pipeline/engine/models/core.py:1142
msgid "对应的进程 ID"
msgstr "The corresponding process ID"

#: pipeline/engine/models/core.py:1173
msgid "pipeline 进程 ID"
msgstr "Pipeline process ID"

#: pipeline/engine/models/core.py:1174 pipeline/engine/models/core.py:1200
#: pipeline/engine/models/core.py:1231
msgid "celery 任务 ID"
msgstr "Celery task ID"

#: pipeline/engine/models/core.py:1199
msgid "schedule ID"
msgstr "Schedule ID"

#: pipeline/engine/models/data.py:45
msgid "对象唯一键"
msgstr "The only key objects"

#: pipeline/engine/models/data.py:46
msgid "对象存储字段"
msgstr "Object storage field"

#: pipeline/engine/models/function.py:54
msgid "功能名称"
msgstr "Function name"

#: pipeline/engine/models/function.py:55
msgid "功能描述"
msgstr "Functional Description"

#: pipeline/engine/models/function.py:56
msgid "是否激活"
msgstr "Activate or not"

#: pipeline/log/models.py:43
msgid "logger 名称"
msgstr "Logger name"

#: pipeline/log/models.py:47
msgid "输出时间"
msgstr "Output Time"

#: pipeline/log/models.py:50
msgid "节点执行历史 ID"
msgstr "Node execution history ID"

#: pipeline/models.py:77
msgid "快照字符串的md5sum"
msgstr "Md5sum string of snapshots"

#: pipeline/models.py:84 pipeline/models.py:85
msgid "模板快照"
msgstr "Template Snapshot"

#: pipeline/models.py:157
#, python-format
msgid "子流程引用链中存在循环引用：%s"
msgstr "Subprocess reference exists circular reference chain: %s"

#: pipeline/models.py:274
msgid "模板名称"
msgstr "Template Name"

#: pipeline/models.py:277 pipeline/models.py:621
msgid "描述"
msgstr "Description"

#: pipeline/models.py:279 pipeline/models.py:505
msgid "修改时间"
msgstr "Change the time"

#: pipeline/models.py:281
msgid "模板结构数据"
msgstr "Template structure data"

#: pipeline/models.py:283
msgid "是否含有子流程"
msgstr "If it contains sub-processes"

#: pipeline/models.py:284
msgid "表示当前模板是否删除"
msgstr "Indicate whether to delete the current template"

#: pipeline/models.py:289 pipeline/models.py:290 pipeline/models.py:613
msgid "Pipeline模板"
msgstr "Pipeline Templates"

#: pipeline/models.py:432
msgid "根模板ID"
msgstr "Root template ID"

#: pipeline/models.py:433
msgid "子流程模板ID"
msgstr "Sub-process template ID"

#: pipeline/models.py:434
msgid "子流程节点 ID"
msgstr "Sub process node ID"

#: pipeline/models.py:435 pipeline/models.py:459 pipeline/models.py:489
msgid "快照字符串的md5"
msgstr "Md5 snapshot string"

#: pipeline/models.py:487
msgid "模板 ID"
msgstr "Template ID"

#: pipeline/models.py:488
msgid "模板数据 ID"
msgstr "Template data ID"

#: pipeline/models.py:490
msgid "添加日期"
msgstr "Add date"

#: pipeline/models.py:501
msgid "对应模板 ID"
msgstr "The corresponding template ID"

#: pipeline/models.py:503
msgid "方案唯一ID"
msgstr "Unique ID program"

#: pipeline/models.py:504
msgid "方案名称"
msgstr "Program Name"

#: pipeline/models.py:506
msgid "方案数据"
msgstr "Program data"

#: pipeline/models.py:619
msgid "启动时间"
msgstr "Start Time"

#: pipeline/models.py:622
msgid "是否已经启动"
msgstr "Started or not"

#: pipeline/models.py:623
msgid "是否已经完成"
msgstr "Completed or not"

#: pipeline/models.py:624
msgid "是否已经撤销"
msgstr "Revoked or not"

#: pipeline/models.py:625
msgid "是否已经删除"
msgstr "Deleted or not"

#: pipeline/models.py:625
msgid "表示当前实例是否删除"
msgstr "Indicates whether the current instance is deleted or not"

#: pipeline/models.py:631
msgid "实例结构数据，指向实例对应的模板的结构数据"
msgstr ""
"Examples of data structures, data structures point to an instance of a "
"template corresponding to"

#: pipeline/models.py:639
msgid "用于实例执行的结构数据"
msgstr "Examples of data structures for execution"

#: pipeline/models.py:647
msgid "提前计算好的一些流程结构数据"
msgstr "Some good advance computing process structure data"

#: pipeline/models.py:654 pipeline/models.py:655
msgid "Pipeline实例"
msgstr "Pipeline examples"

#: pipeline/validators/connection.py:37
#, python-format
msgid "不能连接%s类型节点\n"
msgstr "Type nodes not connected %s\n"

#: pipeline/validators/connection.py:39
#, python-format
msgid "节点的入度最大为%s，最小为%s\n"
msgstr "The maximum in degree of the node is%s, and the minimum is %s\n"

#: pipeline/validators/connection.py:41
#, python-format
msgid "节点的出度最大为%s，最小为%s\n"
msgstr "The maximum outgoing degree of a node is%s, and the minimum is %s\n"

#: pipeline/validators/gateway.py:130 pipeline/validators/gateway.py:183
msgid "并行网关中的分支网关必须将所有分支汇聚到一个汇聚网关"
msgstr ""
"Gateway Gateway parallel branches must all branches converge to a converged "
"gateway"

#: pipeline/validators/gateway.py:193
msgid "分支网关的所有分支第一个遇到的汇聚网关必须是同一个"
msgstr ""
"All branches of the first convergence gateway encountered branch gateway "
"must be the same"

#: pipeline/validators/gateway.py:203
msgid "非法网关，请检查其分支是否符合规则"
msgstr "Illegal gateway, check its compliance with the rules of the branch"

#: pipeline/validators/gateway.py:220
msgid "汇聚网关只能汇聚来自同一个并行网关的分支"
msgstr ""
"Aggregation Gateway only brought together from the same branch of a parallel "
"gateway"

#: pipeline/validators/gateway.py:225
msgid "汇聚网关没有汇聚其对应的并行网关的所有分支"
msgstr ""
"No aggregation aggregation gateway corresponding parallel gateways all "
"branches"

#: pipeline/validators/gateway.py:319
msgid "无法获取该网关距离开始节点的距离"
msgstr "Unable to get away from the gateway from the start node"

#: pipeline/variable_framework/models.py:25
msgid "变量编码"
msgstr "Variable ID"

#: pipeline/variable_framework/models.py:26
msgid "变量是否可用"
msgstr "Variable available or not"

#: pipeline/variable_framework/models.py:29
#: pipeline/variable_framework/models.py:30
msgid "Variable变量"
msgstr "Variable variable"

#~ msgid "用户名"
#~ msgstr "Username"

#~ msgid "访问版本"
#~ msgstr "Access Version"

#~ msgid "版本日志访问记录"
#~ msgstr "Version log access record"

#~ msgid "返回"
#~ msgstr "Return"

#~ msgid "访问出错，请联系管理员。"
#~ msgstr "Access error, please contact administrator."

#~ msgid "日志列表获取成功"
#~ msgstr "Log list fetched successfully"

#~ msgid "日志版本文件没找到，请联系管理员"
#~ msgstr "Log version file not found, please contact administrator"

#~ msgid "日志详情获取成功"
#~ msgstr "Log details fetched successfully"
