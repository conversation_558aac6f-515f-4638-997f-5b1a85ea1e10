.bk-login-dialog {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .7);
  .close-btn {
    position: absolute;
    width: 20px;
    height: 20px;
    background: #fff;
    top: -10px;
    right: -10px;
    border-radius: 50%;
    text-align: center;
    line-height: 20px;
    font-size: 13px;
    cursor: pointer;
    border: 1px solid #ddd;
    .bk-icon {
      margin-left: 1px;
    }
    &:hover {
      background-color: #3c96ff;
      color: #fff;
      border-color: #3c96ff;
    }
  }
  .close-btn:hover {
    box-shadow: 0 0 10px rgba(255, 255, 255, .5);
  }
  .closeBtn img {
    width: 8px;
    margin-top: 0;
    display: inline-block;
    position: relative;
    top: -1px;
  }
  .bk-login-wrapper {
    display: inline-block;
    background: #fff;
    border: 0;
    width: 450px;
    height: 450px;
    padding-right: 5px;
    margin: -225px auto auto -225px;
    top: 50%;
    left: 50%;
    position: relative;
    border-radius: 4px;
    iframe {
      text-align: center;
      margin-left: -25px;
      margin-top: -5px;
      border: 0;
    }
  }
}
