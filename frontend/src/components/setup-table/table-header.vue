<template>
  <div :class="[
    'setup-header',
    {
      'is-center': !parentProp,
      'batch-able': batch
    }
  ]">
    <div
      ref="tipSpan"
      :class="{
        'header-label': true,
        'header-label-required': required,
        'header-label-tips': <PERSON><PERSON><PERSON>(tips)
      }">
      <span :title="$t(label)">{{ $t(label) }}</span>
      <!-- <div v-bk-overflow-tips><span>{{ $t(label) }}</span></div> -->
    </div>
    <bk-popover
      class="batch-btn"
      v-if="batch"
      theme="light batch-edit"
      trigger="manual"
      placement="bottom"
      ref="batch"
      :tippy-options="{ 'hideOnClick': false }"
      :on-show="handleOnShow"
      :on-hide="handleOnHide">
      <span
        v-bk-tooltips.top="{
          'content': $t('批量编辑', { title: '' }),
          'delay': [300, 0]
        }"
        class="batch-icon nodeman-icon nc-bulk-edit"
        :class="{ 'active': isActive }"
        @click="handleBatchClick"
        v-show="isBatchIconShow">
      </span>
      <template #content>
        <div class="batch-edit">
          <template v-if="type === 'password'">
            <div class="batch-edit-title">
              {{ $t('批量编辑', { title: $t('密码') }) }}
            </div>
            <div class="batch-edit-content" v-if="isShow">
              <InstallInputType
                v-bind="{ type: 'password' }"
                v-model="value"
                @enter="handleBatchConfirm" />
              <div class="tip">{{ subTitle }}</div>
            </div>
            <div class="batch-edit-title">
              {{ $t('批量编辑', { title: $t('密钥') }) }}
            </div>
            <div class="batch-edit-content" v-if="isShow">
              <InstallInputType
                v-bind="{ type: 'file' }"
                @upload-change="handleFileChange" />
              <div class="tip">{{ $t('仅对密钥认证生效') }}</div>
            </div>
          </template>
          <template v-else>
            <div class="batch-edit-title">
              {{ $t('批量编辑', { title: $t(label) }) }}
            </div>
            <div class="batch-edit-content" v-if="isShow">
              <InstallInputType
                v-bind="{
                  type: type,
                  options: options,
                  multiple: multiple,
                  appendSlot: appendSlot,
                  searchable: searchable,
                  placeholder
                }"
                v-model="value"
                @enter="handleBatchConfirm" />
              <div class="tip" v-if="subTitle">{{ subTitle }}</div>
            </div>
          </template>
          <div class="batch-edit-footer">
            <bk-button text ext-cls="footer-confirm" @click="handleBatchConfirm">{{ $t('确定') }}</bk-button>
            <bk-button text ext-cls="footer-cancel" class="ml20" @click="handleBatchCancel">{{ $t('取消') }}</bk-button>
          </div>
        </div>
      </template>
    </bk-popover>
    <div v-show="false">
      <section ref="tipRef">
        <TableHeaderTip v-if="parentTip" joint-tip :tips="parentTip" />
        <TableHeaderTip :tips="tips" :row="focusRow" :remark="remark" @batch="handleTipsBatch" />
      </section>
    </div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Prop, Emit, Ref } from 'vue-property-decorator';
import { bus } from '@/common/bus';
import InstallInputType from './install-input-type.vue';
import { IFileInfo, ISetupRow } from '@/types';
import TableHeaderTip from './table-header-tip.vue';
import { getConfigRemark } from '@/config/config';

@Component({
  name: 'table-header',
  components: {
    InstallInputType,
    TableHeaderTip,
  },
})

export default class TableHeader extends Vue {
  @Prop({ type: String, default: '' }) private readonly prop!: string;
  @Prop({ type: String, default: '' }) private readonly tips!: string; // 是否有悬浮提示
  @Prop({ type: String, default: '' }) private readonly label!: string; // 表头label
  @Prop({ type: String, default: '' }) private readonly parentProp!: string;
  @Prop({ type: String, default: '' }) private readonly parentTip!: string;
  @Prop({ type: Boolean, default: false }) private readonly required!: boolean; // 是否显示必填标识
  @Prop({ type: Boolean, default: false }) private readonly batch!: boolean; // 是否有批量编辑框
  @Prop({ type: Boolean, default: true }) private readonly isBatchIconShow!: boolean; // 是否显示批量编辑图标
  @Prop({ type: String, default: '' }) private readonly type!: string; // 批量编辑框类型
  @Prop({ type: String, default: '' }) private readonly subTitle!: string; // 批量编辑提示信息
  @Prop({ type: Array, default: () => ([]) }) private options!: Dictionary[]; // 下拉框数据源
  @Prop({ type: Boolean, default: false }) private readonly multiple!: boolean; // 是否支持多选
  @Prop({ type: Boolean, default: false }) private readonly searchable!: boolean;
  @Prop({ type: String, default: '' }) private readonly placeholder!: string;
  @Prop({ type: String, default: '' }) private readonly appendSlot!: string;
  @Prop({ type: Object, default: () => ({}) }) private readonly focusRow!: ISetupRow;
  @Prop({ type: String, default: 'light setup-tips' }) private readonly tipTheme!: string;

  @Ref('batch') private readonly batchRef!: any;
  @Ref('tipSpan') private readonly tipSpan!: any;
  @Ref('tipRef') private readonly tipRef!: any;

  private isActive = false; // 当前批量编辑icon是否激活
  private value = '';
  private isShow = false;
  private fileInfo: null | IFileInfo = null; // 密钥信息
  private popoverInstance: any = null;
  // 切换Popover的触发方式, 规避无法切换导致的问题 (setProps函数的替代方案, 低版tippy本无此方法)
  private tipsTrigger: 'mouseenter' | 'manual' = 'mouseenter';

  private get remark() {
    return getConfigRemark(`${this.prop}__description`, this.focusRow.os_type);
  }

  private created() {
    bus.$on('batch-btn-click', this.hidePopover); // 只出现一个弹框
  }
  private mounted() {
    if (this.tips) {
      this.popoverInstance = this.$bkPopover(this.tipSpan, {
        content: this.tipRef,
        allowHTML: true,
        trigger: 'mouseenter',
        arrow: true,
        theme: this.tipTheme,
        maxWidth: 274,
        sticky: true,
        duration: [275, 0],
        interactive: true,
        boundary: 'window',
        placement: 'top',
        hideOnClick: true,
        onHide: () => this.tipsTrigger === 'mouseenter',
      });
    }
  }
  private beforeDestroy() {
    this.popoverInstance && this.popoverInstance.destroy();
  }

  public handleBatchClick() {
    if (this.isActive) {
      this.handleBatchCancel();
    } else {
      this.batchRef && this.batchRef.instance.show();
      this.isActive = true;
      bus.$emit('batch-btn-click', this);
    }
  }
  @Emit('confirm')
  public handleBatchConfirm() {
    this.handleBatchCancel();
    return { value: this.value, fileInfo: this.fileInfo };
  }
  @Emit('confirm')
  public handleTipsBatch() {
    return { focusRow: this.focusRow };
  }
  public handleBatch(value: any) {
    this.value = value;
    this.handleBatchConfirm();
  }
  public handleBatchCancel() {
    this.isActive = false;
    this.batchRef && this.batchRef.instance.hide();
  }
  public handleOnShow() {
    this.value = '';
    this.isShow = true;
  }
  public handleOnHide() {
    this.isShow = false;
  }
  public hidePopover(instance: any) {
    if (instance === this) return;
    this.handleBatchCancel();
  }
  public handleFileChange(fileInfo: IFileInfo) {
    this.fileInfo = fileInfo;
  }
  public tipsShow() {
    if ((this.tips || this.parentTip) && this.popoverInstance) {
      this.tipsTrigger = 'manual';
      this.popoverInstance.show();
    }
  }
  public tipsHide() {
    this.tipsTrigger = 'mouseenter';
    this.popoverInstance && this.popoverInstance.hide();
  }
}
</script>
<style lang="postcss" scoped>
  @import "@/css/mixins/nodeman.css";

  .setup-header {
    display: inline-flex;
    align-items: center;
    font-weight: normal;
    text-align: left;
    width: 100%;
    overflow: hidden;
    &.is-center {
      justify-content: center;
    }
    .header-label {
      position: relative;
      display: inline-block;
      line-height: 16px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      &-required {
        padding-right: 6px;
        &::after {
          content: "*";
          color: #ff5656;
          position: absolute;
          top: 2px;
          right: 0;
        }
      }
      &-tips {
        border-bottom: 1px dashed #c4c6cc;
        cursor: default;
      }
    }
    .batch-btn {
      flex-shrink: 0;
    }
    .batch-icon {
      margin-left: 6px;
      font-size: 16px;
      color: #979ba5;
      cursor: pointer;
      outline: 0;
      &:hover {
        color: #3a84ff;
      }
      &.active {
        color: #3a84ff;
      }
    }
  }
</style>
