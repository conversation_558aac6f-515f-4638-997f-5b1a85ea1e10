.russian-dolls-form {
  padding-right: 32px;

  .bk-form-item + .bk-form-item {
    margin-top: 16px;
    
  }
  .bk-form-item {
    &.is-required .bk-label:after {
      content: none;
    }
    &.is-required >.bk-label {
      position: relative;

      &:after {
          height: 8px;
          line-height: 1;
          content: '*';
          color: #EA3636;
          font-size: 12px;
          position: absolute;
          display: inline-block;
          vertical-align: middle;
          top: 50%;
          transform: translate(3px, -50%);
      }
    }
  }

  .nodeman-icon {
    font-size: 16px;
    cursor: pointer;
    &:hover:not(.disabled) {
      color: #3a84ff;
    }

    &.disabled {
      color: #c4c6cc;
      cursor: not-allowed;
    }
    & + .nodeman-icon {
      margin-left: 10px
    }
  }
}

.item-array {
  & > .bk-form-content {
    display: flex;
    align-items: center;
  }
  .array-child-group {
    display: flex;
    flex-direction: column;
    flex: 1;
    /* padding-top: 32px; */
  }
  .array-child {
    display: flex;
    align-items: center;
    flex: 1;
    padding-right: 20px;
    background: #f5f7fa;
    
    & + .array-content-add {
      margin-top: 12px;
    }
  }
  .is-error {
    .array-content-add {
      border-color: #ea3636;
      /* color: #ea3636; */
    }
    .error-tip {
      font-size: 12px;
      color: #ea3636;
      line-height: 18px;
      margin: 2px 0 0;
    }
  }
}

.array-content-add {
  flex: 1;
  border: 1px dashed #3a84ff;
  border-radius: 2px;
  color: #3a84ff;
  cursor: pointer;
  text-align: center;
  .nodeman-icon {
    font-size: 16px;
  }
}

.array-child  {

  .item-object {
    flex: 1;
    /* 特殊操作 */
    position: relative;
    padding: 16px 0;
  }
}

.array-child + .array-child {
  margin-top: 12px;
}

.array-content-delete {
  position: absolute;
  left: calc(100% - 20px);
}
.child-btns {
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
  width: 50px;
  font-size: 16px;
  flex-shrink: 0;
}

/* 选择第一层的 .array-content-delete */
.russian-dolls-form > .item-object > .item-array > .bk-form-content > .array-child-group > .array-child > .array-content-delete {
  left: 100%;
}

.item-key-value {
  .flex {
    background-color: #f5f7fa;
  }
  .flex1 {
    display: flex;
    position: relative;
    .is-error {
      & input[type=text] {
        border-color: #ff5656;
        color: #ff5656;
      }
      &.error-tip {
        position: absolute;
        right: 8px;
        font-size: 16px;
        color: #ea3636;
        cursor: pointer;
      }
    }
  }
}