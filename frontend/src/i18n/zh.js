/* eslint-disable max-len */
export const dedicated = {
  蓝鲸节点管理: '节点管理',
  管控区域: '管控区域',
  管控区域名称: '管控区域名称',
  云服务商: '云服务商',
  阿里云: '阿里云',
  AWS: 'AWS',
  腾讯云: '腾讯云',
};

// 导航 & 菜单 - 单词都首字母大写
export const nav = {
  // Agent
  nav_节点管理: '节点管理',
  nav_Agent状态: 'Agent状态',
  nav_Excel导入安装: 'Excel 导入安装',
  nav_重装Agent: '重装 Agent',
  nav_重载Agent配置: '重载 Agent 配置',
  nav_卸载Agent: '卸载 Agent',

  nav_插件管理: '插件管理',
  nav_插件状态: '插件状态',
  nav_插件部署: '插件部署',
  nav_新建策略: '新建策略',
  nav_编辑策略: '编辑策略',
  nav_已有策略: '已有策略',
  nav_调整目标: '调整目标',
  nav_升级部署: '升级部署',
  nav_新建部署策略: '新建部署策略',
  nav_启用预览: '启用预览',
  nav_停用预览: '停用预览',
  nav_卸载并删除预览: '卸载并删除预览',
  nav_失败重试预览: '失败重试预览',
  nav_新建灰度策略: '新建灰度策略',
  nav_编辑灰度策略: '编辑灰度策略',
  nav_发布灰度策略: '发布灰度策略',
  nav_删除灰度策略: '删除灰度策略',
  nav_安装或更新插件: '安装或更新插件',
  nav_启动插件预览: '启动插件预览',
  nav_停止插件预览: '停止插件预览',
  nav_重载插件预览: '重载插件预览',
  nav_重启插件预览: '重启插件预览',
  nav_托管插件预览: '托管插件预览',
  nav_取消托管插件预览: '取消托管插件预览',

  nav_插件包: '插件包',
  nav_插件包解析: '插件包解析',
  nav_资源配额: '资源配额',
  nav_编辑资源配额: '编辑资源配额',

  nav_历史: '历史',
  nav_任务历史: '任务历史',

  // BK-Net
  nav_管控区域管理: '管控区域管理',
  nav_新建管控区域: '新建管控区域',
  nav_编辑管控区域: '编辑管控区域',
  nav_安装Proxy: '安装 Proxy',
  nav_替换Proxy: '替换 Proxy（{ip}）',

  // Configuration
  nav_全局配置: '全局配置',
  nav_GSE环境管理: 'GSE 环境管理',
  nav_编辑接入点: '编辑接入点',
  nav_新增接入点: '新增接入点',
  nav_自监控: '自监控',
};

// export const langDialog = {
//   ok: 'OK',
//   cancel: 'Cancel',
// };

export default {
  ...dedicated,
  ...nav,

  退出登录: '退出登录',
  请求出错: '请求出错',
  请求的资源没有权限: '请求的资源没有权限',
  请求的资源不存在: '请求的资源不存在',
  系统出现异常: '系统出现异常',
  创建: '创建',
  搜索名称: '搜索名称，ID，云服务商，接入点',
  管控区域别名: '管控区域别名',
  别名: '别名',
  管控区域ID: '管控区域ID',
  可用Proxy数量: '可用Proxy数量',
  接入点: '接入点',
  操作: '操作',
  编辑: '编辑',
  删除: '删除',
  管控区域管理提示一: '管控区域： 互相之间能直接通信的一组服务器单元，如企业内的局域网、公有云VPC（虚拟私有网络）。Proxy： 「管控区域」对外通信的代理节点。',
  变更管控区域原有的接入点: '变更「管控区域」原有的接入点：',
  将会重装该管控区域下所有Proxy: '将会重装该「管控区域」下所有 Proxy',
  删除成功: '删除成功',
  确定删除该管控区域: '确定删除该「管控区域」？',
  自动选择接入点: '自动选择接入点',
  下一步: '下一步',
  取消: '取消',
  必填项: '必填项',
  请输入: '请输入',
  请选择: '请选择',
  待选择: '待选择',
  搜索别名: '搜索别名',
  加载中: '加载中',
  安装Proxy: '安装 Proxy',
  稍后安装: '稍后安装',
  proxy安装建议: '建议至少安装 2 个以上 Proxy 以保障更好的可用性',
  proxy数量提示: '！该「管控区域」只有 1 个Proxy，建议安装 2 个以上保障更高可用性',
  proxy数量提示0: '！该「管控区域」没有可用Proxy，建议安装 2 个以上保障更高可用性',
  确定删除: '确定删除？',
  Proxy状态: 'Proxy 状态',
  Proxy版本: 'Proxy 版本',
  重装: '重装',
  安装重装: '安装/重装',
  替换: '替换',
  更多: '更多',
  认证资料过期不可操作: '认证资料过期不可{type}, 请编辑',
  未选中管控区域: '未选中管控区域',
  下线: '下线',
  移除: '移除',
  重启: '重启',
  重载配置: '重载配置',
  正常: '正常',
  异常: '异常',
  未知: '未知',
  网络场景: '网络场景',
  安装信息: '安装信息',
  保存1天: '保存1天',
  长期保存: '长期保存',
  操作系统: '操作系统',
  登录端口: '登录端口',
  登录账号: '登录账号',
  归属业务: '归属业务',
  运维部门: '运维部门',
  安装: '安装',
  上一步: '上一步',
  简单网络: '简单网络',
  复杂网络: '复杂网络',
  安装要求提示: '可使用telnet<IP><PORT>来确认端口是否可通',
  基础信息: '基础信息',
  服务信息: '服务信息',
  内网IPv4提示: '用于与Agent通信的IP地址',
  IP不符合规范: 'IP格式不正确',
  认证方式: '认证方式',
  '密码/密钥': '密钥/密码',
  登录IP提示: `目标主机的用于登录进行 Proxy 安装的 IP 地址，区别于记录在 CMDB 中的 IP；<br />${$DHCP ? '支持 IPv4、IPv6' : '仅支持 IPv4'}。`,
  外网IP: '外网IP',
  外网IPv6: '外网IPv6',
  内网网卡IP: '内网网卡IP',
  外网网卡IP: '外网网卡IP',
  登录IP: '登录IP',
  数据IP: '数据IP',
  数据IP提示: 'Agent采集数据的IP地址， 默认绑定到内网网卡所在的IP， 如有特殊需求可单独制定',
  确定: '确定',
  批量编辑: '批量编辑{title}',
  安装Agent: '安装 Agent',
  卸载: '卸载',
  正在重装: '正在重装',
  正在升级: '正在升级',
  复制: '复制',
  复制IPv4: '复制 IPv4',
  复制IPv6: '复制 IPv6',
  复制ProxyIP: '复制 Proxy IP',
  请输入IP: '请输入IP',
  请选择管控区域: '请选择管控区域',
  勾选IP: '勾选IP',
  所有IP: '所有IP',
  异常IP: '异常IP',
  批量: '批量',
  不同安装方式的Agent不能统一批量操作: '不同安装方式的Agent不能统一批量操作',
  业务: '业务',
  Agent状态: 'Agent状态',
  Agent版本: 'Agent版本',
  安装到: '安装到',
  Excel导入: 'Excel导入',
  安装要求: '安装要求',
  Agent安装要求tips: '安装{0}需要开启一定的网络访问策略。完成{0}信息后，请点击{1}查看相应的安装指南。如需更高级的配置，请单击{2}',
  Proxy安装要求tips: '安装Proxy需要开通一定的网络访问策略，完成Proxy信息填写后，查看相应的安装指引请点击{0}',
  表格展示设置: '表格展示设置 ',
  表格展示设置tips: '，更多高级配置可点击 ',
  全部区域: '全部区域',
  展开更多: '展开更多',
  收起更多: '收起更多',
  隐藏管控区域: '隐藏管控区域',
  保存: '保存',
  过滤原因: '原因',
  导出: '导出',
  将文件拖到此处或: '将文件拖到此处或 {0}',
  上传文件: '上传文件',
  点击上传: '点击上传',
  仅支持xlsx格式的文件下载: '仅支持 .xlsx 格式的文件，下载{0}',
  模板文件: '模板文件',
  导入: '导入',
  文件不能超过: '文件不能超过{size}MB',
  解析文件失败: '解析文件失败',
  管控区域显示设置: '管控区域显示设置',
  替换Proxy提示一: '替换 Proxy 为组合操作，基本流程如下：1.安装新 Proxy；2.更新所有连接到旧 Proxy 的 Agent 的配置文件；3.卸载 Proxy 并从节点管理移除',
  替换Proxy提示二: '因需要更新所有连接到旧 Proxy 的 Agent 配置文件，所有流程会随Agent数量的增加而增加，请耐心等待。',
  别名提示: '为优化「管控区域」名称过长不易查看的问题，可设置简短易记的别名用于展示。别名可编辑，不同业务下的「管控区域」可有相同的别名。',
  不能超过32个字符: '不能超过32个字符',
  端口范围: '端口范围 {range} ',
  安装成功: '安装成功',
  选择业务: '选择业务',
  IP复制成功: '{num}个IP复制成功',
  复制成功Object: '复制成功（{0} 个{1})',
  资源池: '资源池',
  仅对密码认证生效: '仅对密码认证生效',
  仅对密钥认证生效: '仅对密钥认证生效',
  本页全选: '本页全选',
  跨页全选: '跨页全选',
  即可隐藏: '即可隐藏',
  新建: '新建',
  新增: '新增',
  普通安装: '普通安装',
  Excel导入安装: 'Excel 导入安装',
  选择管控区域: '选择管控区域',
  excel导入提示: '1.Excel 中若有外网 IP 字段, 外网 IP 将会自动注册到 CMDB 中；2.Excel 中的接入点字段可以为空，为空时，会自动选择接入点。',
  提交: '提交',
  管控区域创建成功: '管控区域创建成功',
  仍需安装Proxy才能够正常使用: '仍需安装 Proxy 才能够正常使用',
  // 继续安装Proxy: '继续安装 Proxy',
  搜索管控区域名称: '搜索名称',
  暂无数据: '暂无数据',
  自动拉取: '自动拉取',
  Agent数量: 'Agent 数量',
  删除禁用提示: '该「管控区域」下已有主机(Proxy或P-Agent)',
  添加Agent版本包: '添加 Agent 版本包',
  版本包文件类型: '支持 “tgz” ”tar.gz” 扩展名的文件',
  不再提示: '不再提示',
  多ip输入提示: '多IP可用，、空格空行换行分隔',
  可选: '（可选）',
  正在运行: '正在运行',
  自动选择: '自动选择',
  服务状态: '服务状态',
  监听端口: '监听端口',
  TCP连接数: 'TCP 连接数',
  直连区域: '直连区域',
  非直连区域: '非直连区域',
  冲突校验: '{prop}冲突',
  冲突reg: '冲突',
  冲突: '冲突',
  对应校验: '与 {label} 数量不相等',
  卸载Agent: '卸载 Agent',
  过滤IP提示: '已导入 {imported} 项 Agent 安装信息，多余字段将不会录入到 CMDB 中 {firstIp} 等 {filter} 个IP因为已安装而被系统自动过滤',
  删除完成提示: '{success} 个删除成功, {fail} 个删除失败',
  确定移除选择的主机: '确定移除选择的主机？',
  上传成功: '上传成功',
  Excel数据为空: 'Excel数据为空',
  列缺失: '[ {label} ] 列缺失，可使用最新模板文件重试',
  确定卸载该主机: '确定卸载该主机？',
  错误: '错误',
  检测网络连通性: '可使用telnet<IP><PORT>来确认端口是否可通',
  确定重启选择的主机: '确定重启选择的主机？',
  确认重载所选主机的配置: '确认重载所选主机的配置？',
  重启成功: '重启成功',
  蓝鲸版权: 'Copyright © 2012-{year} Tencent BlueKing. All Rights Reserved. {version}',
  确定重装选择的主机: '确定重装选择的主机？',
  全部过滤提示: '主机不符合安装要求，创建任务失败。',
  忽略详情: '忽略主机列表',
  查看详情: '查看详情',
  确定离开当前页: '确定离开当前页？',
  离开将会导致未保存的信息丢失: '离开将会导致未保存的信息丢失',
  节点个数: '{0} 个节点',
  IP个数: '{0} 个IP',
  已选: '已选',
  共: '共',
  选择所有: '选择所有',
  条: '条',
  取消选择所有数据: '取消选择所有数据',
  // '当前管控区域对此业务不可见': '当前管控区域对此业务不可见',
  部分过滤提示: '{firstIp} 等 {total} 个 IP 被系统自动忽略，可在执行状态筛选查看',
  表格设置: '表格设置',
  字段显示设置: '字段显示设置',
  字号设置: '字号设置',
  标准: '标准',
  偏大: '偏大',
  最多选8项: '（最多选8项）',
  全选: '全选',
  浏览器不支持本地存储: '浏览器不支持本地存储',
  版本包: '版本包',
  上传: '上传',
  已选条数: '(已选 {0} 个)',
  自动发现: '自动发现',
  管控区域详情: '管控区域详情',
  升级: '升级',
  升级Agent: '升级 Agent',
  确定升级选择的主机: '确定升级选择的主机？',
  请确认是否批量重启: '请确认是否批量重启',
  请确认是否重启: '请确认是否重启',
  单条确认重启提示: '请确认是否重启 {ip} 的Agent？',
  批量确认重启提示: '请确认是否重启{ip}等 {num} 个IP的Agent？',
  重启lower: '重启',
  卸载lower: '卸载',
  升级lower: '升级',
  移除lower: '移除',
  请确认是否操作: '请确认是否{type}？',
  请确认是否批量操作: '请确认是否批量{type}？',
  单条确认操作提示: '{type} {ip} 的Agent{suffix}',
  批量确认操作提示: '{type}{ip}等 {num} 个IP的Agent{suffix}',
  到最新版本: '到最新版本',
  收起按钮提示: '安装要求',
  agent列表搜索: '搜索IP、管控区域ID:IP、Agent ID、管控区域等',
  密码过期: '密码过期',
  最新执行日志: '最新执行日志',
  业务拓扑: '业务拓扑',
  出口IP提示: `用于与GSE Server的数据传输，如果是公有云服务器，一般为 <b>公网IP</b> 或 <b>NAT IP</b>，请根据实际情况填写；<br />${$DHCP ? '支持 多IPv4、仅支持使用英文逗号分隔<br />支持 IPv6<br />如有多个外网IP, 全都填写' : '仅支持IPv4（支持多Pv4，用英文逗号分隔）'}。`,
  出口IP: '出口IP',
  多外网IP提示: '仅作展示,通过出口IP修改',
  多外网IP: '多外网IP',
  Proxy未安装: '该「管控区域」未安装 Proxy，请重新选择「管控区域」，或 {0}',
  前往安装: '前往安装',
  Proxy过期: '该「管控区域」下的 Proxy 的认证资料已失效，请重新选择「管控区域」，或 {0}',
  前往更新: '前往更新',
  无匹配数据: '无匹配数据',
  产品文档: '产品文档',
  版本日志: '版本日志',
  问题反馈: '问题反馈',
  开源社区: '开源社区',
  当前版本: '当前版本',
  安装到业务: '安装到业务',
  接入点已在管控区域中设定: '接入点已在「管控区域」中设定',
  全部业务: '全部业务',
  留空默认为内网IP: '留空默认为内网IP',
  认证资料期限: '认证资料期限',
  过期: '过期',
  有效: '有效',
  修改登录信息: '修改登录信息',
  留空默认不更新密码: '留空默认不更新密码',
  Proxy安装: '安装Proxy',
  未安装: '未安装',
  点击前往安装: '点击前往安装',
  未安装Proxy: '未安装 Proxy',
  存在异常Proxy: '存在异常 Proxy',
  认证资料过期: '密码或秘钥过期',
  Linux64位: 'Linux(64位)',
  仅支持Linux64位操作系统: '仅支持 Linux 64位操作系统',
  安装时间: '安装时间',
  复制超时: '复制超时，',
  请重试: '请重试',
  '复制出错，再重新复制一遍吧': '复制失败了！请重试',
  '密钥方式仅对Linux/AIX系统生效': '密钥方式仅对Linux/AIX系统生效',
  请输入业务名称或业务ID: '请输入 业务名称 或 业务ID',
  请选择业务: '请选择业务',
  联系BK助手: '联系BK助手',
  蓝鲸桌面: '蓝鲸桌面',
  技术支持: '技术支持',
  社区论坛: '社区论坛',
  产品官网: '产品官网',
  关注我们: '关注我们',
  加速设置: '加速设置',
  启动BT传输加速: '启动BT传输加速',
  传输限速: '传输限速',
  传输限速Unit: '传输限速 M/s',
  高级: '高级设置',
  高级设置: '高级设置',
  批量高级设置: '批量高级设置',
  BT节点探测: 'BT节点探测',
  BT节点探测提示: '开启 BT 传输后可大大提升 Agent通过文件管道进行文件分发的传输效率， 同时相应提高agent安装任务的执行速度。\n\n注：跨 VPC 网络环境可能会占用专线带宽，建议此场景下关闭 BT 传输',
  启用: '启用',
  停用: '停用',
  无法查看完整Agent信息: '该「管控区域」存在无权限业务，故无法完整查看 Agent 信息。',
  临时文件目录: '临时文件目录',
  供proxy文件分发临时使用后台定期进行清理建议预留至少磁盘空间: '供 proxy 文件分发临时使用，后台定期进行清理。建议预留至少 1G 磁盘空间',
  编辑成功如需加载最新配置请执行proxy重载: '编辑成功，如需加载最新配置请执行 proxy 重载',
  全: '全',
  数据压缩: '数据压缩',
  数据压缩tip: '开启数据压缩后，所有通过数据管道传输的日志采集数据的流量都将进行压缩，可一定程度上降低数据上报所带来的带宽压力。\n\n注：开启后会造成少量CPU占用的增加，上报效率上可能会发生毫秒级的轻微延迟。\n该配置仅针对agent 2.0，agent 1.0开启数据压缩后将不会生效。',

  // agent安装
  主机IPTip: '主机 IP',
  主机属性: '主机属性',
  登录信息: '登录信息',
  批量应用: '批量应用',
  内网IP: '内网 IP',
  内网IPv4: '内网 IPv4',
  内网IPv6: '内网 IPv6',
  '「登录IP」': '「登录 IP」',
  '「内网IPv4」': '「内网IPv4」',
  '「内网IPv6」': '「内网IPv6」',
  业务属性: '业务属性',
  管控区域属性: '管控区域属性',
  传输信息: '传输信息',
  agentSetupHostIp: $DHCP
    ? '目标主机的IP地址，数据将同步至 CMDB，{1}{2}不能同时为空。\n\n注：{0}未填写时优先取{1}地址作为{0}，若{1}未填写，则取{2}为{0}。若主机有多网卡，用于登录的 IP 地址与 CMDB 中记录的 IP 不一致时，需要单独指定该主机的{0}用于进行 Agent 安装另行设置。'
    : '目标主机的 IPv4 地址，数据将同步至 CMDB。\n\n注：{0}未填写时，默认取{1}地址为{0}。若主机有多网卡，用于登录的 IP 地址与 CMDB 中记录的 IP 不一致时，需要单独指定该主机的{0}用于进行 Agent 安装另行设置。',
  agentSetupLoginInfo: '用于登录到目标主机上进行 Agent 操作：\nLinux 仅支持 SSH 方式；\nWindows 支持 WMI / SSH（仅直连）。',
  agentSetupInnerIp: $DHCP
    ? '目标主机的 IP 地址，数据将同步至 CMDB，{1}{2}至少填一个\n\n注：{0}未填写时优先取{1}作为{0}，若主机有多网卡，用于登录的 IP 地址与 CMDB 中记录的 IP 不一致时，需要单独指定该主机的{0}用于进行 Agent 安装另行设置'
    : '目标主机的 IPv4 地址，数据将同步至 CMDB。\n\n注：{0}未填写时，默认取{1}地址为{0}。若主机有多网卡，用于登录的 IP 地址与 CMDB 中记录的 IP 不一致时，需要单独指定该主机的{0}用于进行 Agent 安装另行设置。',
  agentSetupInnerIPv6: '目标主机的 IPv6 地址，数据将同步至 CMDB，{1}{2}至少填一个\n\n注：{1}未填写时取{2}地址作为{0}，若主机有多网卡，用于登录的 IP 地址与 CMDB 中记录的 IP 不一致时，需要单独指定该主机的{0}用于进行 Agent 安装另行设置。{0}未填写时优先取{1}',
  agentSetupLoginAccount: '登录到目标主机上所使用的用户\nssh {0}@******** -p 22',
  agentSetupLoginIp: `目标主机的用于登录进行 Agent 安装的 IP 地址，区别于记录在 CMDB 中的 IP；\n${$DHCP ? '支持 IPv4、IPv6' : '仅支持 IPv4'}。`,
  agentSetupPort: 'ssh root@******** -p {0}\n当前环境推荐：{1}{2}{3}',
  agentSetupKey: '用于登录到目标主机的凭据，例：\nssh root@******** -p 22\n> {0}\n或\nssh root@******** -p 22 -i {1}',
  agentSetupPwd: '用于登录到目标机器的密码\nssh root@******** -p 22\n> {0}',
  agentSetupFile: '用于登录到目标机器的密钥ssh root@******** -i {0}',
  补充说明tips: '\n补充说明：\n{0}',
  IP数量不一致: 'IP数量不一致',
  proxySetupHostIp: `用于与该「管控区域」下 Agent 通信的内网IP地址， ${$DHCP ? '{1}{2}不能同时为空' : '仅支持 IPv4'}。`,
  proxySetupLoginInfo: '用于 SSH 到目标主机进行 Proxy 操作：\nLinux 仅支持 SSH 方式；',

  安装方式: '安装方式',
  远程安装: '远程安装',
  远程安装提示: '需要提供登录信息，远程完成安装',
  手动安装: '手动安装',
  手动安装提示: '无需提供登录信息，自行在服务器上执行给定命令完成安装',
  勾选管控区域的ProxyIP: '勾选管控区域的 Proxy IP',
  所有管控区域的ProxyIP: '所有管控区域的 Proxy IP',
  重装Proxy: '重装 Proxy',

  寻址方式: '寻址方式',
  动态寻址: '动态寻址',
  动态: '动态',
  静态: '静态',
  接入点互斥: '主机处于 GSE{0}接入点，{1} 的接入点禁用',

  // 安装通道
  安装通道: '安装通道',
  默认通道: '默认通道',
  复制ProxyIP: '复制ProxyIP',
  通道名称: '通道名称',
  新建安装通道: '新建安装通道',
  编辑安装通道: '编辑安装通道',
  默认通道: '默认通道',
  删除通道: '删除通道',
  确认删除此通道: '确认删除此通道？',
  通过此通道安装的Agent不受影响: '通过此通道安装的 Agent 不受影响',
  该通道的节点尚未进行手动部署请根据指引部署方可使用此通道进行agent安装: '该通道的节点尚未进行手动部署，请根据指引部署，方可使用此通道进行 agent 安装',
  安装通道Desc: '在特殊复杂网络下，目标主机无法与「管控区域」内主机直接连通，可通过指定「安装通道」进行 Agent 安装。 默认选择「默认通道」即可。',
  部署指引: '部署指引',
  节点IP: '节点 IP',
  上游节点: '上游节点',
  上游节点信息: '上游节点信息',
  高级选项: '高级选项',
  通道上游节点: '通道上游节点',
  通道上游节点tips: '在复杂网络架构下，需要指定安装通道本身的上游代理地址，用于上报 P-Agent 日志等信息至节点管理后台',

  // agent管理 - 自动发现
  待处理: '待处理',
  已忽略: '已忽略',
  请选择条件搜索: '请选择条件搜索',
  管控区域_ID: '管控区域 （ID）',
  发现时间: '发现时间',
  确认录入: '确认录入',
  忽略: '忽略',
  恢复: '恢复',
  自动发现topTip: '本列表为后台自动发现的安装了Agent的主机，且该主机均未加入到CMDB中。确认后，主机将录入到CMDB资源池中，到CMDB中分配主机到业务，会自动同步到节点管理中。',
  请确认: '请确认',
  IP地址: 'IP地址',
  是否忽略此IP: '是否忽略此IP',
  是否忽略选中的IP: '是否忽略选中的IP',
  是否录入此IP: '是否录入此IP',
  是否录入选中的IP: '是否录入选中的IP',
  是否恢复此IP: '是否恢复此IP',
  是否恢复选中的IP: '是否恢复选中的IP',
  认证信息过期: '认证信息过期',
  节点来源: '节点来源',
  请输入关键字: '请输入关键字',
  复制成功: '复制成功',
  没有需要复制的内容: '没有需要复制的内容',
  查看任务详情: '查看任务详情',

  // 全局配置 - GSE-环境管理
  默认: '默认',
  gseTopTips1: '1.安装直连 Agent及Proxy时，从内网URL下载安装包；安装非直连 Agent时，从外网URL下载安装包',
  gseTopTips2: '2.GSE Server可以根据区域，部署中继Server；创建「管控区域」时可以选择就近的接入点',
  确认删除此接入点: '确认删除此接入点？',
  删除接入点成功: '删除接入点成功',
  Server信息: 'Server信息',
  Server序号: 'Server序号',
  Agent信息: 'Agent信息',
  安装包: '安装包',
  内网: '内网: ',
  外网: '外网: ',
  hostid路径: 'hostid路径',
  安装路径: '安装路径',
  运行时路径: '运行时路径',
  日志文件路径: '日志文件路径',
  数据文件路径: '数据文件路径',
  临时文件路径: '临时文件路径',
  接入点名称: '接入点名称',
  用户创建的接入点: '用户创建的接入点',
  请输入Server的内网IP: '请输入{type} Server的内网IP',
  请输入Server的外网IP: '请输入{type} Server的外网IP',
  Agent安装包: 'Agent安装包',
  Agent包URL: 'Agent包URL',
  Agent包服务器目录: 'Agent包服务器目录',
  请输入服务器目录: '请输入服务器目录',
  服务器目录: '服务器目录',
  请输入内网下载URL: '请输入内网下载URL',
  请输入外网下载URL: '请输入外网下载URL',
  测试Server及URL可用性: '测试Server及URL可用性',
  测试结果: '测试结果：',
  地址格式不正确: '地址格式不正确',
  IP格式不正确: 'IP格式不正确',
  URL格式不正确: 'URL格式不正确',
  Agent信息Linux: 'Agent 信息（Linux）',
  Agent信息Windows: 'Agent 信息（Windows）',
  Proxy信息: 'Proxy 信息',
  Proxy上的安装包: 'Proxy 上的安装包',
  接入点说明: '接入点说明',
  接入点说明placeholder: '描述新增的接入点信息，注意事项等',
  Zookeeper集群地址: 'Zookeeper集群地址',
  Zookeeper用户名: 'Zookeeper用户名',
  Zookeeper密码: 'Zookeeper密码',
  Zookeeper序号: 'Zookeeper序号',
  请输入Zookeeper主机的IP: '请输入Zookeeper主机的IP',
  请输入Zookeeper主机的端口号: '请输入Zookeeper主机的端口号',
  端口: '端口',
  集群地址: '集群地址',
  确认: '确认',
  修改接入点成功: '修改接入点成功',
  新增接入点成功: '新增接入点成功',
  LinuxIpc校验不正确: '以 / 开头，至少包含一级目录，且只能包含大小写英文、数字、_和.',
  请输入不小于零的整数: '请输入不小于零的整数',
  winIpc校验不正确: '不小于零的整数',
  路径格式不正确: '以 / 开头，至少包含两级目录，且为长度不超过{num}的大小写英文、数字、连字符和下划线',
  Linux路径格式不正确: '以 / 开头，至少包含一级目录，且为长度不超过16的大小写英文、数字和下划线',
  Linux安装路径格式不正确: '以 / 开头，至少包含两级目录，且为长度不超过16的大小写英文、数字和下划线',
  windows路径格式不正确: '以 [c-fC-F]:\\ 开头，至少包含一级目录，且为长度不超过16的大小写英文、数字和下划线',
  包名称格式不正确: '仅包含大小写英文、数字、连字符和下划线，且以 .tgz 结尾',
  长度为3_32的字符: '长度为3-32的中文、英文、下划线',
  linux安装路径: '长度为3-32的中文、英文、下划线',
  不能以如下内容开头: ' 前缀不能匹配到以下路径：{path}',
  加载插件基础信息: '加载插件基础信息',
  加载插件基础信息成功: '加载插件基础信息成功',
  该接入点被使用中无法删除: '该接入点被使用中，无法删除',
  序号: '序号',

  // 自监控
  后台服务器: '后台服务器',
  节点管理后台服务状态: '节点管理后台服务状态',
  节点管理SaaS依赖周边组件状态: '节点管理 SaaS 依赖周边组件状态',
  可能原因: '可能原因',
  解决方案: '解决方案',
  接口描述: '接口描述：',
  测试参数: '测试参数：',
  出错信息: '出错信息：',

  // 全局配置 - 任务配置
  安装PAgent超时时间: '安装P-Agent超时时间',
  安装PAgent超时时间tip: '安装P-Agent，通过在Proxy机器上个执行Job作业，作业脚本默认使用40个并发。安装过程中，需要将安装包(大约80M)从Proxy推送到目标服务器。安装所需时长取决于批量安装主机的数量和Proxy到P-Agent之间的带宽。',
  安装Agent超时时间: '安装Agent超时时间',
  安装Agent超时时间tip: '安装Agent 时，需要将安装包和工具（共约 40M）分发到目标机，安装时长约 120 秒 + 分发时长',
  安装Proxy超时时间: '安装Proxy超时时间',
  安装Proxy超时时间tip: '安装Proxy时，需需要从Agent下载URL下载安装包(约150 - 180M), 安装时长取决于Proxy到Nginx之间的带宽，安装时长约180秒 +下载时长',
  安装下载限速: '安装下载限速',
  安装下载限速tip: '0代表不限速',
  并行安装数: '并行安装数',
  格式不正确: '格式不正确',
  后台日志记录级别: '后台日志记录级别',
  重置: '重置',
  整数范围校验提示: '请输入{min}-{max}的整数',
  整数最小值校验提示: '请输入不小于{min}的整数',
  保存配置成功: '保存配置成功',
  任务配置: '任务配置',
  地域信息: '地域信息',
  区域: '区域',
  城市: '城市',
  外网回调: '外网回调',
  外网回调地址: '外网回调地址',
  请输入外网回调地址: '请输入外网回调地址',
  内网回调地址: '内网回调地址',
  内网回调: '内网回调',
  请输入内网回调地址: '请输入内网回调地址',
  请输入以backend结尾的URL地址: '请输入以/backend结尾的URL地址',

  // 任务历史
  机器数量: '(共 {0} 个)',
  任务ID: '任务ID',
  任务类型: '任务类型',
  执行者: '执行者',
  我: '我',
  执行时间: '执行时间',
  耗时: '耗时',
  总耗时: '总耗时',
  执行状态: '执行状态',
  总数: '总数',
  成功数: '成功数',
  失败数: '失败数',
  忽略数: '忽略数',
  个执行中: '个执行中',
  个等待执行: '个等待执行',
  个成功: '个成功',
  个失败: '个失败',
  个忽略: '个忽略',
  状态未知: '状态未知',
  正在: '正在',
  '安装P-Agent': '安装P-Agent',
  '重启P-Agent': '重启P-Agent',
  '重启-Agent': '重启-Agent',
  正在执行: '正在执行',
  失败: '失败',
  已终止: '已终止',
  执行成功: '执行成功',
  执行失败: '执行失败',
  部分失败: '部分失败',
  等待执行: '等待执行',
  重试: '重试',
  单步重试: '单步重试',
  整体重试: '整体重试',
  终止: '终止',
  失败批量重试: '失败批量重试',
  重试所有失败IP: '重试所有失败IP',
  任务详情: '任务详情：',
  任务详情Item: '任务详情',
  终止任务: '终止任务',
  执行情况: '执行情况',
  多个IP以逗号分隔: '多个IP以逗号分隔',
  查看日志: '查看日志',
  下载日志: '下载日志',
  按Esc即可退出全屏模式: '按 Esc 即可退出全屏模式',
  步骤: '步骤',
  跳过: '跳过',
  全屏: '全屏',
  退出全屏: '退出全屏',
  执行日志: '执行日志',
  执行日志标题: '{ip} {jobType} 的执行日志',
  所属管控区域: '(所属管控区域:{cloud})',
  任务日志topTip: '点击步骤可以快速定位对应日志内容；点击日志右下方的 {0} 可以快速定位到最新日志内容。',
  安装PROXY: '安装 Proxy',
  安装AGENT: '安装 Agent',
  重启AGENT: '重启 Agent',
  重启PROXY: '重启 Proxy',
  替换proxy: '替换 Proxy',
  重装PROXY: '重装 Proxy',
  重装AGENT: '重装 Agent',
  升级PROXY: '升级 Proxy',
  升级AGENT: '升级 Agent',
  已忽略IP无日志详情: '已忽略IP无日志详情',
  已忽略信息提示: '{ip} 等 {num} 个 IP 已被系统自动忽略，可在执行状态筛选查看',
  被忽略IP: '被忽略IP',
  失败IP: '失败IP',
  成功IP: '成功IP',
  复制失败common: '复制失败，不存在可复制内容',
  IP复制失败: 'IP复制失败，未存在所选类型IP',
  安装命令: '安装命令',
  查看命令: '查看命令',
  命令btn: '手动安装',
  卸载命令btn: '手动卸载',
  复制命令: '复制命令',
  复制安装命令: '复制安装命令',
  命令复制失败: '命令复制失败',
  命令复制成功: '命令复制成功',
  刷新安装状态: '刷新安装状态',
  展开全部: '展开全部',
  收起全部: '收起全部',
  下载工具包: '下载工具包',
  关闭: '关闭',
  // '手动安装任务Tips': '安装任务已经创建，您可以复制相关命令在Proxy服务器中执行完成安装，',
  手动安装任务Tips: '安装任务已经创建，请点击列表的“手动安装”继续操作',
  手动卸载任务Tips: '卸载任务已经创建，请点击列表的“手动卸载”继续操作',
  手动安装任务UrlTips: '但需确认所在服务器可访问：{url}',
  安装命令dialogTips: '本弹窗关闭后，如需再次查看，请点击任务详情页的',
  手动: '手动',
  远程: '远程',
  手动操作sliderTitle: '手动{0} {1}',
  手动操作指引: '您可以在{0}通过以下步骤手动{1} {2}：',
  执行以下操作命令: '执行以下操作命令，',
  网络开通策略: '网络开通策略',
  windowsStrategy1Before: '将 ',
  windowsStrategy1After: ' 传至 C:\\tmp\\（如果出现access is denied，需赋予文件可执行权限）',
  windowsStrategy2: '输入：cmd 切换到控制台命令行',
  替换成: '替换成',
  真实数据: '真实数据',
  再执行: '再执行。',
  账号: '账号',
  源地址: '源地址',
  目标地址: '目标地址',
  协议: '协议',
  用途: '用途',
  备注: '备注',
  点击复制: '点击复制',
  管控区域未安装Proxy: '「管控区域」未安装Proxy',
  请选择接入点: '请选择接入点',
  该管控区域下暂未安装Proxy: '该「管控区域」下暂未安装Proxy',
  获取配置: '获取配置',
  任务服务端口: '任务服务端口',
  数据上报端口: '数据上报端口',
  监听随机端口: '监听随机端口',
  ping告警数据上报端口: 'ping 告警数据上报端口',
  BT传输: 'BT 传输',
  BT传输可不开通: 'BT 传输，可不开通',
  同一子网: '同一子网',
  TCP上报日志获取配置: 'TCP 上报日志/获取配置',
  nginx下载nginx代理: 'nginx下载/nginx代理',
  节点管理后台: '节点管理后台',
  Agent安装Tip1: '目标机器必须是 Linux 系统',
  Agent安装Tip2: 'Linux 系统使用非 root 安装时，要求可以免密sudo执行/tmp/setup_agent.sh 脚本',
  Agent安装Tip3: '直连 Proxy 端：保证与蓝鲸服务端以下端口互通， 可使用telnet<IP><PORT>来确认端口是否可通',
  Proxy安装Tip1: 'Linux/Unix 系统使用非 root 安装时，要求可以免密sudo执行/tmp/setup_agent.sh 脚本',
  Proxy安装Tip2: '直连Agent端：保证与蓝鲸服务端双向全通，若存在策略限制，可新增管控区域来管理',
  agent数量: '(共 {num} 个)',
  请将操作指令中的数据替换再执行: '请将操作指令中的 {0} {1} {2} 替换成 {3} 再执行。',
  等待手动操作查看: '等待手动操作，查看 {0}',
  操作指引: '操作指引',
  // 展示手动卸载命令按钮需要
  手动安装Guide: '安装',
  手动卸载Guide: '卸载',
  手动卸载Agent: '卸载Agent',
  手动卸载Proxy: '卸载Proxy',

  特殊字符限制: '特殊字符限制',
  密码: '密码',
  密钥: '密钥',
  铁将军: '铁将军',
  铁将军IEG: '铁将军（IEG专用）',
  安装策略: '安装策略',
  文件列表: '文件列表',
  下载全部: '下载全部',
  支持的操作系统: '支持Linx/Windows/AIX操作系统',
  开通Nginx策略提示内网: '开通到 Nginx 内网 {url} 的网络策略',
  开通Nginx策略提示外网: '开通到 Nginx 外网 {url} 的网络策略',
  开通Linux策略提示: '若为Linux/AIX：开通网络策略，允许APPO内网 IP {ip} SSH到目标机器（Linux/AIX)',
  开通Windows策略提示: '若为Windows：开通网络策略，允许APPO内网 IP {ip} 访问服务器的 {port} 端口，并打开Windows的C盘默认共享，命令：net share c$=c: ',
  登录要求提示: 'Windows必须使用Administrator账号，Linux可以使用root，或可免密sudo执行命令的用户',
  Linux脚本执行权限提示: 'Linux/Unix系统使用非root安装时，要求可以免密sudo执行/tmp/setup_agent.sh 脚本',
  APPO外网到Proxy使用端口: '从 APPO 外网 IP {appoIp} 到 Proxy IP {proxyIp} 使用SSH端口 {port}',
  Proxy到GSE使用端口: '从 Proxy IP {proxyIp} 到 GSE Server IP {gseIp} 使用端口 {port}',
  Gse到Proxy使用端口: '从 GSE Server IP {gseIp} 到 Proxy IP {proxyIp} 使用端口 {port}',
  修改成功: '修改成功',
  过滤列表: '过滤列表',

  // 插件管理
  选择操作: '选择操作',
  插件类型: '插件类型',
  插件操作: '插件操作',
  选择插件: '选择插件',
  选择要变更的服务: '选择要变更的服务',
  选择要更新的插件: '选择要更新的插件',
  选择新包: '选择新包',
  进程管理: '进程管理',
  插件更新: '插件更新',
  插件: '插件',
  保留原有配置文件: '保留原有配置文件',
  '仅更新文件，不重启进程': '仅更新文件，不重启进程',
  '增量更新(仅覆盖)': '增量更新(仅覆盖)',
  '覆盖更新(先删除原目录后覆盖)': '覆盖更新(先删除原目录后覆盖)',
  立即执行: '立即执行',
  保存并执行: '保存并执行',
  请联系运维: '请联系运维',
  '将插件包上传到中控机的/data/src/目录并解压': '将插件包上传到中控机的/data/src/目录并解压',
  '然后执行./bkeec(或bkcec) pack gse_plugin 更新插件信息到/data/src下': '然后执行./bkeec(或bkcec) pack gse_plugin 更新插件信息到/data/src下',
  请选择操作: '请选择操作',
  请选择新包: '请选择新包',
  请选择插件: '请选择插件',
  更新时间: '更新时间',
  值: '值',
  暂无其他插件信息: '暂无其他插件信息',
  操作系统为必须的筛选条件: '操作系统必选',
  选择主机: '选择主机',
  选择操作类型: '选择操作类型',
  精确: '精确',
  节点类型: '节点类型',
  启动: '启动',
  停止: '停止',
  重载: '重载',
  托管: '托管',
  取消托管: '取消托管',
  官方插件: '官方插件',
  第三方插件: '第三方插件',
  脚本插件: '脚本插件',
  无可操作的主机: '无可操作的主机',
  请先选择主机: '请先选择主机',

  // exception组件
  没有数据: '没有数据',
  没有权限: '没有权限',
  搜索为空: '搜索为空',
  数据异常: '数据异常',
  去申请: '去申请',
  权限不足: 'Sorry，您的权限不足！',
  页面找不到了: '页面找不到了！',
  服务器维护中: '服务器维护中，请稍后重试',
  功能正在建设中: '功能正在建设中···',
  可以尝试调整关键词或清空筛选条件: '可以尝试 调整关键词 或 {0}',
  清空筛选条件: '清空筛选条件',
  搜索结果为空: '搜索结果为空',
  获取数据异常: '获取数据异常',
  刷新: '刷新',

  // auth
  申请业务权限: '申请业务权限',
  该操作需要以下权限: '该操作需要以下权限',
  系统: '系统',
  需要申请的权限: '需要申请的权限',
  关联的资源实例: '关联的资源实例',
  无数据: '无数据',
  新建auth: '新建{type}',
  编辑auth: '编辑{type}',
  查看auth: '查看{type}',
  删除auth: '删除{type}',
  页面auth: '您没有相应业务的访问权限，请前往申请相关业务权限',
  查看agentAuth: '您没有Agent查看权限，请前往申请相关权限',
  操作agentAuth: '您没有Agent操作权限，请前往申请相关权限',
  查看插件Auth: '您没有插件状态查询权限，请前往申请相关权限',
  查看任务历史Auth: '您没有任务历史的访问权限，请前往申请相关权限',
  全局任务Auth: '您没有任务配置的权限，请前往申请相关权限',
  安装proxyAuth: '您没有Proxy操作权限，请前往申请相关权限',

  // 插件状态
  插件状态: '插件状态',
  部署策略: '部署策略',
  部署: '部署',
  选择部署策略: '选择部署策略',
  全部部署策略: '全部部署策略',
  搜索主机: '搜索主机',
  成功: '成功',
  队列中: '队列中',
  部署中: '部署中',
  已选择插件: '已选择 {0} 台主机',
  停用插件: '停用插件',
  重启插件: '重启插件',
  重载插件: '重载插件',
  下发配额: '下发配额',
  清除所有数据: '清除所有数据',
  操作范围: '操作范围',
  Agent状态异常: 'Agent 状态异常',
  前往Agent状态查看: '前往 Agent状态 查看',
  未安装lower: '未安装',
  状态异常: '状态异常',
  Agent无法选择: 'Agent{0}，无法选择',
  前往部署策略模块查看: '前往 部署策略 模块查看',
  取消注册: '取消注册',
  部署目录: '部署目录',
  自动部署: '自动部署',
  配置模板: '配置模板',
  配置模板版本: '配置模板版本',
  选择所有数据: '选择所有数据',
  批量插件操作: '批量插件操作',
  安装或更新: '安装/更新',
  任务状态: '任务状态',

  // 策略
  确定操作选择的主机插件: '确定操作选择的主机插件',
  升级版本: '升级版本',
  升级目标: '升级目标',
  参数配置: '参数配置',
  添加: '添加',
  执行预览: '执行预览',
  部署目标: '部署目标',
  部署版本: '部署版本',
  选择版本: '选择版本',
  '静态 - IP 选择': '静态 - IP 选择',
  '动态 - 拓扑选择': '动态 - 拓扑选择',
  已部署: '已部署',
  全部主机: '全部主机',
  搜索拓扑节点: '搜索拓扑节点',
  策略部署范围: '策略部署范围',
  点击展开: '点击展开',
  移除所有: '移除所有',
  子节点名称: '子节点名称',
  动态拓扑: '动态拓扑',
  静态拓扑: '静态拓扑',
  动态选择: '动态 - 拓扑选择',
  静态选择: '静态 - IP 选择',
  静态IP数量: '静态IP {0}',
  动态拓扑数量: '动态拓扑 {0}',
  不能混用: '不能混用',
  根节点: '根节点',
  '输入主机IP/主机名/操作系统/管控区域进行搜索...': '输入主机IP/主机名/操作系统/「管控区域」进行搜索...',
  主机IP: '主机IP',
  主机名: '主机名',
  已选项预览: '已选项预览',
  关联主机预览: '关联主机预览',
  个: '个',
  所属业务: '所属业务',
  操作类型: '操作类型',
  插件版本: '插件版本',
  当前部署数量: '当前部署数量',
  选择目标版本: '选择目标版本',
  配置版本: '配置版本',
  机器部署数量: '机器部署数量',
  版本描述: '版本描述',
  选择部署版本: '选择部署版本',
  插件包版本: '插件包版本',
  测试版本提示line1: '管理员定义不稳定版本，',
  测试版本提示line2: '请在风险可控范围内使用',
  复用到所有相同表单: '复用到所有相同表单',
  恢复默认值: '恢复默认值',
  系统内置公共变量: '系统内置公共变量',
  Windows: 'Windows',
  Linux: 'Linux',
  子配置: '子配置',
  请选择子配置项: '请选择子配置项',
  插入系统内置变量: '插入系统内置变量',
  内置变量: '内置变量',
  变量值: '变量值',
  变量描述: '变量描述',
  部署插件: '部署插件',
  插件功能: '插件功能',
  选择策略: '选择策略',
  去调整目标: '去调整目标',
  去新建策略: '去新建策略',
  已有策略: '已有策略',
  该插件暂无部署策略: '该插件暂无部署策略',
  基于已有的策略调整目标: '基于已有的策略, 调整目标',
  已有策略不适用继续新建: '已有策略不适用, 继续新建',
  策略名称: '策略名称',
  最近修改人: '最近修改人',
  最近部署时间: '最近部署时间',
  已部署目标: '已部署目标',
  全业务: '全业务',
  主机: '主机',
  节点: '节点',
  插件配置不能为空: '插件配置不能为空',
  目标不能为空: '目标不能为空',
  请输入策略名称: '请输入策略名称',
  添加目标: '添加目标',
  清空: '清空',
  可选子配置: '可选子配置',
  正常agent个数: '{0} 正常',
  异常agent个数: '{0} 异常',
  未安装agent个数: '{0} 未安装 ',
  忽略内容: '将被忽略：{0}{1}{2}',
  忽略内容tip: 'Agent 状态异常、未安装， 将在任务执行中被忽略',
  部署目标必须选择: '部署目标必须选择',
  已经部署最新版本: '已经部署最新版本',
  已部署数: '已部署数',
  测试: '测试',
  节点名称: '节点名称',
  主机数量: '主机数量',
  已是最新版本: '已是最新版本',
  必填参数完整: '必填参数完整',
  通过检测现有主机操作系统已自动选中必须部署的插件包: '通过检测现有主机操作系统，已自动选中必须部署的插件包',
  是否删除此策略: '是否删除此策略？',
  删除操作无法撤回请谨慎操作: '删除操作无法撤回，请谨慎操作！',
  启用策略: '启用策略',
  卸载并删除: '卸载并删除',
  强制删除: '强制删除',
  是否强制删除此策略: '是否强制删除此策略？',
  当前策略下仍关联主机: '当前策略下仍关联 {0} 台主机，强制删除后 主机插件将脱离管控',
  当前部署策略下仍有关联的主机策略将在卸载任务执行成功后删除: '当前部署策略下仍有关联的主机，策略将在卸载任务执行成功后删除',
  目前待卸载的主机全部异常: '目前待卸载的 {0} 台主机 Agent 全部异常，无法正常卸载。如需删除部署策略，可选择强制删除，强制删除后主机插件将脱离管控。',
  操作策略成功: '{0}策略成功！',
  不能低于当前版本: '不能低于当前版本',
  版本未处于正式状态: '版本未处于正式状态',
  部分目标机器已被部署策略管控无法进行操作如需操作请调整对应的部署策略: '部分目标机器已被部署略管控，无法进行操作；如需操作请调整对应的部署策略',
  停用策略dialogTitle: '停用策略：{0}',
  停用策略dialogContentOnly: '仅停用策略，保留插件 {0}',
  停用策略dialogContentAll: '停用策略，同时 {0} 插件',
  运行中: '运行',
  停用策略成功: '停用策略成功',
  删除策略成功: '删除策略成功',
  当前没有可操作的主机: '当前没有可操作的主机',
  关联主机数: '关联主机数',
  托管插件Tips: '在节点管理启动的插件默认会被托管。如插件被人为拉起，可单独执行托管操作',
  停用托管插件Tips: '在节点管理停止的插件默认会取消托管。若想取消托管同时保持插件运行，可单独执行取消托管操作',
  搜索IP管控区域操作系统Agent状态: '搜索 IP、管控区域ID:IP、Agent ID、插件状态等',
  停用策略Tips: '停用策略，将会使已安装的插件脱离策略管控',
  目标版本: '目标版本',
  策略状态: '策略状态',
  发布: '发布',
  灰度: '灰度',
  灰度发布btn提示: '主策略关联的所有 {0} 将调整为当前灰度版本',
  灰度删除btn提示: '取消当前灰度，将 {0} 回滚至主策略版本',
  新建灰度策Tip: '新建灰度策略',
  主策略版本保持不变Tip: '主策略版本保持不变',
  多行国际化: '{0}{1}{2}{3}{4}',
  新建流程中只允许点击下一步: '新建流程中，只允许点击「下一步」',
  后续编辑可快速切换TAB: '后续编辑，可快速切换 TAB',
  查无数据: '查无数据',
  自定义输入: '自定义输入',
  长度不能大于20个中文或40个英文字母: '长度不能大于20个中文或40个英文字母',
  至少选择一个目标: '至少选择一个目标',
  灰度名称: '灰度名称',
  灰度目标: '灰度目标',
  灰度版本: '灰度版本',
  灰度参数: '灰度参数',
  版本回滚: '版本回滚',
  请在左侧勾选IP或者节点: '请在左侧勾选IP或者节点',
  插件操作: '插件操作',
  发布确认: '{0} 发布确认',
  主策略当前版本: '主策略当前版本',
  主策略目标版本: '主策略目标版本',
  将当前灰度版本发布至主策略: '将当前灰度版本发布至主策略',
  已部署插件的主机将被停止: '已部署插件的主机，将被停止',
  当前策略下有主机安装插件失败: '当前策略下有 {0} 台主机安装插件失败，',
  且已超过最大尝试次数: '且已超过最大尝试次数；',
  如需再次尝试安装请点击重试失败: '如需再次尝试安装，请点击 {0}',
  执行删除操作后灰度策略关联的所有将回滚至主策略版本: '执行删除操作后，灰度策略关联的所有 {0} 将 {1} 至主策略版本',
  执行发布操作后主策略关联的所有将调整为当前灰度版本: '执行发布操作后，{0} 关联的所有 {1} 将调整为当前灰度版本',

  // plugin 插件管理
  业务个数: '{0} 个业务:',
  分页文案: '共计{0}条 每页{1}条',
  已选择: '已选择：{0} 个节点、 {1} 台主机',
  已选主机: '已选择 {0} 台主机',
  节点统计: '{1} 正常, {2} 异常, {3} 未安装',
  已选节点: '已选择 {0} 个节点',
  已关联主机: '已关联主机 {0} 台主机',
  已选择部署版本个数: '已选择 {0} 个部署版本',
  共多少个: '(共{0}个)',
  新建策略: '新建策略',
  搜索策略插件: '搜索 部署策略、插件名称',
  包含业务: '包含业务',
  操作账号: '操作账号',
  最近操作时间: '最近操作时间',
  编辑参数: '编辑参数',
  搜索部署策略: '搜索部署策略…',
  已选条数: '已选{0}条',
  选择所有条数: '选择所有{0}条',

  // 插件包
  插件别名: '插件别名',
  插件名称: '插件名称',
  开发商: '开发商',
  已部署节点: '已部署节点',
  数据对接SaaS: '数据对接 SaaS',
  插件描述: '插件描述',
  部署方式: '部署方式',
  导入插件: '导入插件',
  搜索插件别名插件名称: '搜索插件别名、插件名称',
  部署: '部署',
  查看: '查看',
  更新: '更新',
  包名称: '包名称',
  主程序版本: '主程序版本',
  主配置版本: '主配置版本',
  子配置版本: '子配置版本',
  支持系统: '支持系统',
  解析结果: '解析结果',
  插件详情: '插件详情',
  下载插件包: '下载插件包',
  支持部署于: '支持部署于',
  状态: '状态',
  版本管理: '版本管理',
  停用包: '停用包',
  下载: '下载',
  插件包已选个数: '插件包 ( 已选 {0} 个 )',
  解析通过个数: '{0} 个解析通过',
  解析失败个数: '{0} 个解析失败',
  停用插件SaaS链接: '请到：{0} 停用',
  '请输入筛选条件，模糊匹配条件之间用 | 分隔': '请输入筛选条件，模糊匹配条件之间用 | 分隔',
  插件部署方式: '插件部署方式',
  程序版本: '程序版本',
  最新程序版本: '最新程序版本',
  子配置模板: '子配置模板',
  包大小: '包大小',
  更新人: '更新人',
  更新账户: '更新账户',
  已上线: '已上线',
  上线: '上线',
  已停用: '已停用',
  文件类型不符: '文件类型不符',
  去部署: '去部署',
  插件状态: '插件状态',
  插件已停用无法部署: '插件已停用，无法部署',
  启用插件: '启用插件',
  启用包: '启用包',
  安装包列表: '安装包列表',
  确认要停用此插件: '确认要停用此插件？',
  插件停用后将无法再继续进行部署已部署的节点不受影响: '插件停用后，将无法再继续进行部署。已部署的节点不受影响',
  正式: '正式',
  更改状态: '更改状态',
  测试版本要主动选择才能安装: '测试版本，要主动选择才能安装',
  停用版本不可以被部署到新的主机上: '停用版本，不可以被部署到新的主机上',
  插件别名格式不正确: '格式不正确，只能包含汉字、英文、数字、空格和下划线',

  // 资源配额
  无业务权限: '无业务权限',
  申请权限: '申请权限',
  '业务：': '业务：',
  请输入关键词: '请输入关键词',
  总数运行中异常: '总数 / 运行中 / 异常',
  资源配额tip: '资源配额可以预定义插件的 CPU、内存使用限制，用于保证插件进程合理使用机器资源。',
  资源配额规则tip: '具体规则：当插件进程占用资源超出任意一项预设的资源配额时，插件进程会被强制退出。',
  资源配额场景tip: '使用场景：避免插件进程过度占用机器资源，导致影响业务进程正常运行；为插件进程分配合理的资源，使其在不同业务场景下都能按预期工作。',
  CPU配额: 'CPU 配额',
  内存配额: '内存配额',
  CPU配额tip: '每个插件 CPU 使用率上限百分比（总占比，非单核占比）',
  内存配额tip: '每个插件内存使用率上限百分比',
  还原默认: '还原默认',
  CPU配额及单位: 'CPU 配额 (%)',
  内存配额及单位: '内存配额 (%)',
  业: '业',
  集: '集',
  模: '模',
  '已手动调整资源配额（非默认配置）': '已手动调整资源配额（非默认配置）',
  请选择服务模板: '请选择服务模板',
  当前业务暂无服务模版: '当前业务暂无服务模版',
  业务暂无服务模版path: '通过服务模版可以快速将配置应用到同一组服务',
  业务暂无服务模版slot: '前往 {0} 创建服务模版',
  配置平台: '配置平台',

  // 任务历史 - 新
  创建管控区域权限: '创建管控区域权限',
  编辑管控区域权限: '编辑管控区域权限',
  查看管控区域权限: '查看管控区域权限',
  隐藏自动部署任务: '隐藏自动部署任务',
  编辑管控区域权限: '编辑管控区域权限',
  查看管控区域权限: '查看管控区域权限',
  选择日期范围: '选择日期范围',
  搜索任务ID执行者任务类型操作类型部署策略执行状态: '搜索任务ID、IP、执行者、任务类型、操作类型、部署策略、执行状态',
  开始时间: '开始时间',
  执行账号: '执行账号',
  失败重试: '失败重试',
  Agent任务: 'Agent任务',
  Proxy任务: 'Proxy任务',
  插件任务: '插件任务',
  IP模糊搜索: 'IP模糊搜索',
  没有需要填写的参数: '没有需要填写的参数',
  任务创建中请稍后: '任务创建中，请稍后',
  当前操作拆分为多个子任务请关注最终执行结果: '当前操作拆分为多个子任务请关注最终执行结果',
  今天: '今天',
  近7天: '近7天',
  近15天: '近15天',
  近30天: '近30天',
  近1年: '近1年',
  总数成功失败忽略: '总数/成功/失败/忽略',
  目标主机lower: '目标主机',
  在目标主机通过: '在目标主机通过 {0} {1}：',
  操作方式: '{0}方式',

  // form-check
  正常输入内容校验: '长度不超过{0}的中英文、数字、连字符和下划线',
  字符串长度校验: '长度不能大于{0}个中文或{1}个英文字母',
  不小于零的整数: '不小于零的整数',
  Linux路径格式错误: '以/开头，至少包含 {minLevel} 级目录，且为长度不超过 {maxText} 的大小写英文、数字和下划线',
  windows路径格式错误: '以 [c-fC-F]:\\ 开头，至少包含 {minLevel} 级目录，且为长度不超过 {maxText} 的大小写英文、数字和下划线',

  // common
  管控区域IP复制: '管控区域 + {0}',
};
