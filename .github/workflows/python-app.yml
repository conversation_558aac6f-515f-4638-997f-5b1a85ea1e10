# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Flake8 and Black

on: [push, pull_request]

jobs:
  build:

    runs-on: ubuntu-20.04

    steps:
    - uses: actions/checkout@v2
    - name: Set up Python 3.6
      uses: actions/setup-python@v2
      with:
        python-version: 3.6
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black
    - name: Lint with flake8
      run: |
        flake8
    - name: Format with black
      run: |
        black .