@echo off
netsh advfirewall firewall add rule name=IEOD_Outbound_Default_Rule_GSE2_TCP dir=out remoteip="10.0.0.0/8,**********/10,**********/12,*******/8,********/8,********/8,********/8,********/8,********/8,********/8,30.0.0.0/8" protocol=tcp remoteport="28668,28625,28925,28930,10020,10030,60020-60030" profile=public enable=yes action=allow
netsh advfirewall firewall add rule name=IEOD_Outbound_Default_Rule_GSE2_UDP dir=out remoteip="10.0.0.0/8,**********/10,**********/12,*******/8,********/8,********/8,********/8,********/8,********/8,********/8,30.0.0.0/8" protocol=udp remoteport="10020,10030,60020-60030" profile=public enable=yes action=allow
netsh advfirewall firewall add rule name=IEOD_Intbound_Default_Rule_GSE2_TCP dir=in remoteip="10.0.0.0/8,**********/10,**********/12,*******/8,********/8,********/8,********/8,********/8,********/8,********/8,30.0.0.0/8" protocol=tcp localport="10020,10030,60020-60030" profile=public enable=yes action=allow
netsh advfirewall firewall add rule name=IEOD_Intbound_Default_Rule_GSE2_UDP dir=in remoteip="10.0.0.0/8,**********/10,**********/12,*******/8,********/8,********/8,********/8,********/8,********/8,********/8,30.0.0.0/8" protocol=udp localport="10020,10030,60020-60030" profile=public enable=yes action=allow
